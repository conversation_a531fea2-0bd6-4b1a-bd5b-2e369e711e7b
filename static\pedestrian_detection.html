<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>行人检测系统 - 高速公路YOLO智能监控</title>
    
    <!-- Arco Design CSS -->
    <link rel="stylesheet" href="https://unpkg.com/@arco-design/web-react@2.62.0/dist/css/arco.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f7f8fa;
            color: #1d2129;
        }
        
        /* 顶部导航栏 */
        .top-navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0 24px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .navbar-left {
            display: flex;
            align-items: center;
        }
        
        .system-logo {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 20px;
            background-image: url('/static/logo.webp');
            background-size: cover;
            background-position: center;
        }
        
        .system-name {
            font-size: 20px;
            font-weight: 600;
        }
        
        .back-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.3s;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        /* 主内容区 */
        .main-content {
            padding: 24px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        /* 页面标题 */
        .page-header {
            margin-bottom: 24px;
        }
        
        .page-title {
            font-size: 22px;
            font-weight: 600;
            color: #1d2129;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .page-subtitle {
            color: #86909c;
            font-size: 16px;
        }
        
        /* 控制面板 */
        .control-panel {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid #e5e6eb;
        }
        
        .control-header {
            font-size: 18px;
            font-weight: 600;
            color: #1d2129;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .control-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .control-label {
            font-size: 14px;
            font-weight: 500;
            color: #4e5969;
        }
        
        .control-input {
            padding: 8px 12px;
            border: 1px solid #e5e6eb;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .control-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }
        
        .control-buttons {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        
        .btn-success {
            background: #00b42a;
            color: white;
        }
        
        .btn-warning {
            background: #faad14;
            color: white;
        }
        
        .btn-danger {
            background: #f53f3f;
            color: white;
        }
        
        /* 检测网格 */
        .detection-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 24px;
            margin-bottom: 24px;
        }
        
        .detection-panel {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid #e5e6eb;
        }
        
        .detection-panel.active {
            border-color: #667eea;
            box-shadow: 0 4px 16px rgba(102, 126, 234, 0.1);
        }
        
        .panel-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e5e6eb;
        }
        
        .panel-title {
            font-size: 16px;
            font-weight: 600;
            color: #1d2129;
        }
        
        .panel-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active {
            background: #e8f5e8;
            color: #00b42a;
        }
        
        .status-inactive {
            background: #f5f5f5;
            color: #86909c;
        }
        
        .video-display {
            width: 100%;
            height: 280px;
            background: #000;
            border-radius: 8px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            position: relative;
            overflow: hidden;
        }
        
        .detection-info {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .info-item {
            background: #f7f8fa;
            padding: 12px;
            border-radius: 6px;
            text-align: center;
        }
        
        .info-value {
            font-size: 20px;
            font-weight: 600;
            color: #1d2129;
            margin-bottom: 4px;
        }
        
        .info-label {
            font-size: 12px;
            color: #86909c;
        }
        
        .panel-controls {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }
        
        /* 统计面板 */
        .stats-panel {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid #e5e6eb;
            margin-bottom: 24px;
        }
        
        .stats-header {
            font-size: 18px;
            font-weight: 600;
            color: #1d2129;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #f7f8fa, #ffffff);
            border: 1px solid #e5e6eb;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        
        .stat-icon {
            font-size: 32px;
            margin-bottom: 12px;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #1d2129;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 14px;
            color: #86909c;
        }
        
        /* 警报列表 */
        .alert-panel {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid #e5e6eb;
        }
        
        .alert-header {
            font-size: 18px;
            font-weight: 600;
            color: #1d2129;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .alert-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .alert-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 8px;
            border-left: 4px solid;
        }
        
        .alert-high {
            background: #fff2f0;
            border-left-color: #f53f3f;
        }
        
        .alert-medium {
            background: #fff7e6;
            border-left-color: #faad14;
        }
        
        .alert-low {
            background: #f6ffed;
            border-left-color: #00b42a;
        }
        
        .alert-icon {
            margin-right: 12px;
            font-size: 18px;
        }
        
        .alert-content {
            flex: 1;
        }
        
        .alert-title {
            font-weight: 500;
            color: #1d2129;
            margin-bottom: 4px;
        }
        
        .alert-time {
            font-size: 12px;
            color: #86909c;
        }
        
        /* 加载状态 */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 200px;
            color: #86909c;
        }
        
        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #e5e6eb;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 12px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="top-navbar">
        <div class="navbar-left">
            <div class="system-logo"></div>
            <div class="system-name">行人检测系统</div>
        </div>
        <a href="/static/dashboard.html" class="back-btn">
            <span>←</span>
            <span>返回控制台</span>
        </a>
    </nav>
    
    <!-- 主内容区 -->
    <main class="main-content">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">
                <span>🚶</span>
                <span>行人检测系统</span>
            </h1>
            <p class="page-subtitle">高速公路行人入侵检测与预警系统</p>
        </div>
        
        <!-- 控制面板 -->
        <div class="control-panel">
            <div class="control-header">
                <span>🎛️</span>
                <span>检测控制</span>
            </div>
            
            <div class="control-grid">
                <div class="control-group">
                    <label class="control-label">检测敏感度</label>
                    <select class="control-input" id="sensitivity">
                        <option value="low">低敏感度</option>
                        <option value="medium" selected>中等敏感度</option>
                        <option value="high">高敏感度</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label class="control-label">最小检测尺寸</label>
                    <input type="number" class="control-input" id="minSize" value="50" min="20" max="200">
                </div>
                
                <div class="control-group">
                    <label class="control-label">置信度阈值</label>
                    <input type="number" class="control-input" id="confidence" value="0.5" min="0.1" max="1.0" step="0.1">
                </div>
                
                <div class="control-group">
                    <label class="control-label">警报延迟(秒)</label>
                    <input type="number" class="control-input" id="alertDelay" value="3" min="1" max="10">
                </div>
            </div>
            
            <div class="control-buttons">
                <button class="btn btn-primary" id="startDetectionBtn">
                    <span>▶️</span>
                    <span>启动检测</span>
                </button>
                <button class="btn btn-warning" id="pauseDetectionBtn">
                    <span>⏸️</span>
                    <span>暂停检测</span>
                </button>
                <button class="btn btn-danger" id="stopDetectionBtn">
                    <span>⏹️</span>
                    <span>停止检测</span>
                </button>
                <button class="btn btn-success" id="refreshStreamsBtn">
                    <span>🔄</span>
                    <span>刷新视频流</span>
                </button>
            </div>
        </div>
        
        <!-- 统计面板 -->
        <div class="stats-panel">
            <div class="stats-header">
                <span>📊</span>
                <span>检测统计</span>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-value" id="totalPedestrians">0</div>
                    <div class="stat-label">检测到的行人</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">⚠️</div>
                    <div class="stat-value" id="activeAlerts">0</div>
                    <div class="stat-label">活跃警报</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">📹</div>
                    <div class="stat-value" id="activeStreams">0</div>
                    <div class="stat-label">监控路段</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">🎯</div>
                    <div class="stat-value" id="detectionAccuracy">95%</div>
                    <div class="stat-label">检测准确率</div>
                </div>
            </div>
        </div>
        
        <!-- 检测网格 -->
        <div id="detectionGrid" class="detection-grid">
            <!-- 动态生成检测面板 -->
        </div>
        
        <!-- 警报面板 -->
        <div class="alert-panel">
            <div class="alert-header">
                <span>🚨</span>
                <span>实时警报</span>
            </div>
            
            <div id="alertList" class="alert-list">
                <div class="loading">
                    <div class="loading-spinner"></div>
                    <span>等待警报数据...</span>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 全局变量
        let streams = {};
        let detectionActive = false;
        let updateInterval = null;
        let pedestrianCount = 0;
        let alertCount = 0;
        
        // DOM元素
        const detectionGrid = document.getElementById('detectionGrid');
        const alertList = document.getElementById('alertList');
        
        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            refreshStreams();
            setupEventListeners();
            startRealTimeUpdate();
        });
        
        // 设置事件监听器
        function setupEventListeners() {
            document.getElementById('startDetectionBtn').addEventListener('click', startDetection);
            document.getElementById('pauseDetectionBtn').addEventListener('click', pauseDetection);
            document.getElementById('stopDetectionBtn').addEventListener('click', stopDetection);
            document.getElementById('refreshStreamsBtn').addEventListener('click', refreshStreams);
        }
        
        // API请求
        async function apiRequest(url, options = {}) {
            try {
                const response = await fetch(`http://127.0.0.1:5500/api/v1${url}`, {
                    headers: { 'Content-Type': 'application/json' },
                    ...options
                });
                const result = await response.json();
                return result;
            } catch (error) {
                console.error('API请求失败:', error);
                return { success: false, message: error.message };
            }
        }
        
        // 刷新视频流
        async function refreshStreams() {
            const result = await apiRequest('/streams');
            
            if (result.success) {
                streams = result.data;
                updateDetectionGrid();
                updateStats();
                console.log(`刷新成功，共 ${Object.keys(streams).length} 个流`);
            } else {
                console.error(`刷新失败: ${result.message}`);
            }
        }
        
        // 更新检测网格
        function updateDetectionGrid() {
            detectionGrid.innerHTML = '';
            
            for (const [streamId, status] of Object.entries(streams)) {
                if (status.is_running) {
                    const panel = createDetectionPanel(streamId, status);
                    detectionGrid.appendChild(panel);
                }
            }
            
            // 获取检测结果
            updateDetectionResults();
        }
        
        // 创建检测面板
        function createDetectionPanel(streamId, status) {
            const panel = document.createElement('div');
            panel.className = 'detection-panel';
            panel.id = `detection-panel-${streamId}`;
            
            const streamName = getStreamName(streamId);
            
            panel.innerHTML = `
                <div class="panel-header">
                    <div class="panel-title">${streamName}</div>
                    <div class="panel-status status-inactive" id="status-${streamId}">未检测</div>
                </div>
                
                <div class="video-display" id="display-${streamId}" style="position: relative;">
                    <video id="video-${streamId}" width="100%" height="280" style="border-radius: 8px; background: #000;" autoplay muted>
                        <source src="" type="video/mp4">
                        您的浏览器不支持视频播放
                    </video>
                    <canvas id="canvas-${streamId}" width="100%" height="280" style="position: absolute; top: 0; left: 0; border-radius: 8px; display: none;"></canvas>
                </div>
                
                <div class="detection-info">
                    <div class="info-item">
                        <div class="info-value" id="pedestrians-${streamId}">0</div>
                        <div class="info-label">行人数量</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value" id="alerts-${streamId}">0</div>
                        <div class="info-label">警报次数</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value" id="confidence-${streamId}">0%</div>
                        <div class="info-label">置信度</div>
                    </div>
                </div>
                
                <div class="panel-controls">
                    <button class="btn btn-primary btn-small" onclick="enablePedestrianDetection('${streamId}')">
                        启用检测
                    </button>
                    <button class="btn btn-warning btn-small" onclick="disablePedestrianDetection('${streamId}')">
                        禁用检测
                    </button>
                    <button class="btn btn-success btn-small" onclick="testAlert('${streamId}')">
                        测试警报
                    </button>
                </div>
            `;
            
            return panel;
        }
        
        // 启动检测
        async function startDetection() {
            detectionActive = true;
            updateButtonStates();
            
            // 为所有流启用行人检测
            for (const streamId of Object.keys(streams)) {
                await enablePedestrianDetection(streamId);
            }
            
            addAlert('系统启动', '行人检测系统已启动', 'low');
        }
        
        // 暂停检测
        function pauseDetection() {
            detectionActive = false;
            updateButtonStates();
            addAlert('系统暂停', '行人检测系统已暂停', 'medium');
        }
        
        // 停止检测
        async function stopDetection() {
            detectionActive = false;
            updateButtonStates();

            // 停止语音报警
            await stopVoiceAlert();

            // 为所有流禁用行人检测
            for (const streamId of Object.keys(streams)) {
                await disablePedestrianDetection(streamId);
            }

            addAlert('系统停止', '行人检测系统已停止', 'medium');
        }

        // 语音播报函数（使用简单的Web Speech API）
        function speakAlert(message) {
            try {
                if ('speechSynthesis' in window) {
                    const utterance = new SpeechSynthesisUtterance(message);
                    utterance.lang = 'zh-CN';
                    utterance.rate = 1.2;
                    utterance.pitch = 1.1;
                    utterance.volume = 0.8;

                    // 选择中文语音
                    const voices = speechSynthesis.getVoices();
                    const chineseVoice = voices.find(voice => voice.lang.includes('zh'));
                    if (chineseVoice) {
                        utterance.voice = chineseVoice;
                    }

                    speechSynthesis.speak(utterance);
                    console.log('🔊 语音播报:', message);
                }
            } catch (error) {
                console.log('语音播报失败:', error);
            }
        }

        // 停止语音报警
        async function stopVoiceAlert() {
            try {
                // 停止前端语音播放
                stopFrontendVoice();
                console.log('✅ 语音报警已停止');
            } catch (error) {
                console.error('❌ 停止语音报警异常:', error);
            }
        }

        // 停止前端语音播放
        function stopFrontendVoice() {
            try {
                // 停止Web Speech API
                if (window.speechSynthesis) {
                    window.speechSynthesis.cancel();
                    console.log('✅ 已停止Web Speech API语音');
                }

                // 停止所有audio元素
                const audioElements = document.querySelectorAll('audio');
                audioElements.forEach(audio => {
                    audio.pause();
                    audio.currentTime = 0;
                });

                if (audioElements.length > 0) {
                    console.log(`✅ 已停止 ${audioElements.length} 个音频元素`);
                }

            } catch (error) {
                console.error('❌ 停止前端语音失败:', error);
            }
        }
        
        // 启用行人检测
        async function enablePedestrianDetection(streamId) {
            const result = await apiRequest(`/pedestrian/enable/${streamId}`, {
                method: 'POST',
                body: JSON.stringify({
                    sensitivity: document.getElementById('sensitivity').value,
                    min_size: parseInt(document.getElementById('minSize').value),
                    confidence_threshold: parseFloat(document.getElementById('confidence').value),
                    alert_delay: parseInt(document.getElementById('alertDelay').value)
                })
            });
            
            if (result.success) {
                updatePanelStatus(streamId, '检测中', 'active');
                console.log(`行人检测启用成功: ${streamId}`);
            } else {
                console.error(`行人检测启用失败: ${result.message}`);
                addAlert('启用失败', `${getStreamName(streamId)} 行人检测启用失败`, 'high');
            }
        }
        
        // 禁用行人检测
        async function disablePedestrianDetection(streamId) {
            const result = await apiRequest(`/pedestrian/disable/${streamId}`, {
                method: 'POST'
            });
            
            if (result.success) {
                updatePanelStatus(streamId, '未检测', 'inactive');
                console.log(`行人检测禁用成功: ${streamId}`);
            } else {
                console.error(`行人检测禁用失败: ${result.message}`);
            }
        }
        
        // 测试警报
        function testAlert(streamId) {
            const streamName = getStreamName(streamId);
            addAlert('测试警报', `${streamName} 行人检测测试警报`, 'medium');
        }
        
        // 更新面板状态
        function updatePanelStatus(streamId, statusText, statusClass) {
            const statusEl = document.getElementById(`status-${streamId}`);
            const panel = document.getElementById(`detection-panel-${streamId}`);
            
            if (statusEl) {
                statusEl.textContent = statusText;
                statusEl.className = `panel-status status-${statusClass}`;
            }
            
            if (panel) {
                if (statusClass === 'active') {
                    panel.classList.add('active');
                } else {
                    panel.classList.remove('active');
                }
            }
        }
        
        // 更新按钮状态
        function updateButtonStates() {
            const startBtn = document.getElementById('startDetectionBtn');
            const pauseBtn = document.getElementById('pauseDetectionBtn');
            const stopBtn = document.getElementById('stopDetectionBtn');
            
            if (detectionActive) {
                startBtn.disabled = true;
                pauseBtn.disabled = false;
                stopBtn.disabled = false;
            } else {
                startBtn.disabled = false;
                pauseBtn.disabled = true;
                stopBtn.disabled = true;
            }
        }
        
        // 更新检测结果
        async function updateDetectionResults() {
            const result = await apiRequest('/streams/results');

            if (result.success && result.data.results) {
                for (const [streamId, detection] of Object.entries(result.data.results)) {
                    updateStreamDisplay(streamId, detection);

                    // 模拟行人检测结果
                    if (detectionActive) {
                        simulatePedestrianDetection(streamId);
                    }
                }
            }
        }
        
        // 更新流显示
        function updateStreamDisplay(streamId, detection) {
            const display = document.getElementById(`display-${streamId}`);
            
            if (detection.frame_base64 && display) {
                display.innerHTML = `
                    <img src="data:image/jpeg;base64,${detection.frame_base64}" 
                         style="width:100%;height:100%;object-fit:contain;">
                `;
            }
        }
        
        // 模拟行人检测
        function simulatePedestrianDetection(streamId) {
            // 随机生成行人检测结果
            if (Math.random() < 0.1) { // 10% 概率检测到行人
                const pedestrianCount = Math.floor(Math.random() * 3) + 1;
                const confidence = Math.floor(Math.random() * 30) + 70; // 70-100%
                
                updateDetectionInfo(streamId, pedestrianCount, confidence);
                
                if (pedestrianCount > 0) {
                    const streamName = getStreamName(streamId);
                    addAlert('行人检测', `${streamName} 检测到 ${pedestrianCount} 名行人`, 'high');
                }
            }
        }
        
        // 更新检测信息
        function updateDetectionInfo(streamId, pedestrians, confidence) {
            const pedestriansEl = document.getElementById(`pedestrians-${streamId}`);
            const confidenceEl = document.getElementById(`confidence-${streamId}`);
            
            if (pedestriansEl) pedestriansEl.textContent = pedestrians;
            if (confidenceEl) confidenceEl.textContent = confidence + '%';
            
            // 更新全局统计
            pedestrianCount += pedestrians;
            updateStats();
        }
        
        // 添加警报
        function addAlert(title, message, level) {
            const alertItem = document.createElement('div');
            alertItem.className = `alert-item alert-${level}`;
            
            const icon = level === 'high' ? '🚨' : level === 'medium' ? '⚠️' : 'ℹ️';
            const timestamp = new Date().toLocaleTimeString();
            
            alertItem.innerHTML = `
                <div class="alert-icon">${icon}</div>
                <div class="alert-content">
                    <div class="alert-title">${title}: ${message}</div>
                    <div class="alert-time">${timestamp}</div>
                </div>
            `;
            
            // 插入到列表顶部
            if (alertList.firstChild && alertList.firstChild.className !== 'loading') {
                alertList.insertBefore(alertItem, alertList.firstChild);
            } else {
                alertList.innerHTML = '';
                alertList.appendChild(alertItem);
            }
            
            // 限制警报数量
            while (alertList.children.length > 20) {
                alertList.removeChild(alertList.lastChild);
            }
            
            // 更新警报计数
            if (level === 'high' || level === 'medium') {
                alertCount++;
                updateStats();

                // 触发语音播报（仅对重要警报）
                if (level === 'high') {
                    speakAlert(`${title}：${message}`);
                }
            }
        }
        
        // 更新统计信息
        function updateStats() {
            document.getElementById('totalPedestrians').textContent = pedestrianCount;
            document.getElementById('activeAlerts').textContent = alertCount;
            document.getElementById('activeStreams').textContent = Object.keys(streams).length;
        }
        
        // 获取流名称
        function getStreamName(streamId) {
            const names = {
                'hangqian_highway_01': 'K10+500 杭州方向',
                'hangqian_highway_02': 'K15+200 千岛湖方向',
                'hangqian_highway_03': 'K20+800 服务区入口',
                'hangqian_highway_04': 'K68+300 昌化镇段',
                'hangqian_highway_05': 'K89+600 汾口收费站',
                'hangqian_highway_06': 'K105+900 千岛湖镇'
            };
            return names[streamId] || streamId;
        }
        
        // 开始实时更新
        function startRealTimeUpdate() {
            updateInterval = setInterval(() => {
                if (detectionActive) {
                    updateDetectionResults();
                }
            }, 2000); // 每2秒更新一次
        }
        
        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            if (updateInterval) {
                clearInterval(updateInterval);
            }
        });
        
        // 初始化按钮状态
        updateButtonStates();
    </script>
</body>
</html>
