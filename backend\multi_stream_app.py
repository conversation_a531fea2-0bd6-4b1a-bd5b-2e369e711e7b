# -*- coding: utf-8 -*-
"""
多路视频检测独立应用
专注核心功能，避免复杂依赖
"""

from flask import Flask, request, jsonify, render_template_string, redirect, url_for
from flask_cors import CORS
from flask_socketio import Socket<PERSON>
from services.multi_stream_detection import (
    init_multi_stream_service, get_multi_stream_service,
    init_optimized_multi_stream_service, get_optimized_multi_stream_service
)
from services.enhanced_tracking_service import init_enhanced_tracking_service, get_enhanced_tracking_service
from services.data_analysis_service import init_data_analysis_service, get_data_analysis_service
from services.qwen_analysis_service import init_qwen_analysis_service, get_qwen_analysis_service
from services.user_auth_service import init_user_auth_service, get_user_auth_service
from services.voice_alert_service import init_voice_alert_service, get_voice_alert_service, AlertType, VoiceAlertLevel
from services.websocket_service import WebSocketService, init_websocket_service, get_websocket_service
from services.pedestrian_detection_service import init_pedestrian_detection_service, get_pedestrian_detection_service
from services.congestion_analysis_service import init_congestion_analysis_service, get_congestion_analysis_service
from services.congestion_warning_service import init_congestion_warning_service, get_congestion_warning_service
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_app():
    """创建Flask应用"""
    app = Flask(__name__, static_folder='../static', static_url_path='/static')

    # 配置CORS
    CORS(app, resources={
        r"/api/*": {
            "origins": "*",
            "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization"]
        }
    })

    # 初始化SocketIO
    socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')
    app.socketio = socketio
    
    # 初始化所有服务
    print("🎯 初始化多路视频检测服务...")
    multi_stream_service = init_multi_stream_service()

    print("🚀 初始化优化多路视频检测服务...")
    try:
        optimized_multi_stream_service = init_optimized_multi_stream_service()
    except Exception as e:
        print(f"⚠️ 优化服务初始化失败，使用标准服务: {e}")
        optimized_multi_stream_service = None

    print("🔍 初始化追踪服务...")
    tracking_service = init_enhanced_tracking_service()

    print("📊 初始化数据分析服务...")
    data_analysis_service = init_data_analysis_service()

    print("🤖 初始化QWEN分析服务...")
    qwen_service = init_qwen_analysis_service()

    print("🔐 初始化用户认证服务...")
    auth_service = init_user_auth_service()

    print("🔊 初始化语音报警服务...")
    voice_alert_service = init_voice_alert_service()

    print("🚶 初始化行人检测服务...")
    pedestrian_service = init_pedestrian_detection_service()

    print("🚗 初始化拥堵分析服务...")
    congestion_service = init_congestion_analysis_service()

    print("⚠️ 初始化拥堵预警服务...")
    congestion_warning_service = init_congestion_warning_service()

    print("🌐 初始化WebSocket服务...")
    websocket_service = init_websocket_service(socketio)

    # ==================== 页面路由 ====================

    @app.route('/login')
    def login_page():
        """登录页面"""
        login_html = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - 高速公路YOLO智能监控系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-header h1 {
            color: #333;
            margin-bottom: 10px;
        }

        .login-header p {
            color: #666;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .login-btn:hover {
            transform: translateY(-2px);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .error-message {
            color: #e74c3c;
            text-align: center;
            margin-top: 15px;
            display: none;
        }

        .success-message {
            color: #27ae60;
            text-align: center;
            margin-top: 15px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>🚗 系统登录</h1>
            <p>高速公路YOLO智能监控系统</p>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" required>
            </div>

            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required>
            </div>

            <button type="submit" class="login-btn" id="loginBtn">登录</button>
        </form>

        <div class="error-message" id="errorMessage"></div>
        <div class="success-message" id="successMessage"></div>

        <div style="margin-top: 20px; text-align: center; color: #666; font-size: 12px;">
            <p>默认管理员账户: admin / admin123</p>
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const errorMessage = document.getElementById('errorMessage');
            const successMessage = document.getElementById('successMessage');

            // 隐藏之前的消息
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';

            // 禁用按钮
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';

            try {
                const response = await fetch('/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const result = await response.json();

                if (result.success) {
                    // 保存token
                    localStorage.setItem('auth_token', result.token);

                    successMessage.textContent = '登录成功，正在跳转...';
                    successMessage.style.display = 'block';

                    // 跳转到主页
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1000);
                } else {
                    errorMessage.textContent = result.message || '登录失败';
                    errorMessage.style.display = 'block';
                }
            } catch (error) {
                errorMessage.textContent = '网络错误，请重试';
                errorMessage.style.display = 'block';
            } finally {
                // 恢复按钮
                loginBtn.disabled = false;
                loginBtn.textContent = '登录';
            }
        });
    </script>
</body>
</html>
        '''
        return render_template_string(login_html)

    @app.route('/')
    def index():
        """主页 - 重定向到dashboard或登录页面"""
        # 这里可以添加token验证逻辑
        # 暂时直接跳转到dashboard
        return redirect('/static/dashboard.html')

    # ==================== API路由 ====================

    # ==================== 认证API ====================

    @app.route('/api/v1/auth/login', methods=['POST'])
    def login():
        """用户登录"""
        try:
            data = request.get_json()
            username = data.get('username')
            password = data.get('password')

            if not username or not password:
                return jsonify({
                    'success': False,
                    'message': '用户名和密码不能为空'
                }), 400

            result = auth_service.authenticate_user(username, password)

            if result['success']:
                return jsonify(result), 200
            else:
                return jsonify(result), 401

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'登录失败: {str(e)}'
            }), 500

    @app.route('/api/v1/auth/verify', methods=['POST'])
    def verify_token():
        """验证令牌"""
        try:
            auth_header = request.headers.get('Authorization')
            if not auth_header or not auth_header.startswith('Bearer '):
                return jsonify({
                    'success': False,
                    'message': '缺少认证令牌'
                }), 401

            token = auth_header.split(' ')[1]
            result = auth_service.verify_token(token)

            if result['success']:
                return jsonify(result), 200
            else:
                return jsonify(result), 401

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'令牌验证失败: {str(e)}'
            }), 500

    # ==================== 视频流API ====================

    @app.route('/api/v1/streams/optimized/test', methods=['POST'])
    def test_optimized_streams():
        """测试优化的视频流"""
        try:
            optimized_service = get_optimized_multi_stream_service()
            if not optimized_service:
                return jsonify({
                    'success': False,
                    'message': '优化服务未初始化'
                }), 500

            # 测试流配置
            test_streams = [
                {
                    'stream_id': 'hangqian_highway_01_opt',
                    'name': '杭千高速K10+500(优化)',
                    'video_source': 0,  # 使用摄像头
                    'max_fps': 30
                },
                {
                    'stream_id': 'hangqian_highway_02_opt',
                    'name': '杭千高速K25+200(优化)',
                    'video_source': 0,
                    'max_fps': 30
                }
            ]

            results = []
            for stream_config in test_streams:
                success = optimized_service.add_stream(
                    stream_config['stream_id'],
                    stream_config['video_source'],
                    stream_config['max_fps']
                )

                results.append({
                    'stream_id': stream_config['stream_id'],
                    'name': stream_config['name'],
                    'success': success,
                    'status': 'running' if success else 'failed'
                })

            return jsonify({
                'success': True,
                'message': '优化视频流测试启动',
                'streams': results
            })

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'启动优化测试失败: {str(e)}'
            }), 500

    @app.route('/api/v1/streams/optimized/results', methods=['GET'])
    def get_optimized_results():
        """获取优化视频流结果"""
        try:
            optimized_service = get_optimized_multi_stream_service()
            if not optimized_service:
                return jsonify({
                    'success': False,
                    'message': '优化服务未初始化'
                }), 500

            # 获取所有结果
            results = optimized_service.get_all_results()
            performance_stats = optimized_service.get_performance_stats()

            return jsonify({
                'success': True,
                'results': results,
                'performance': performance_stats,
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取优化结果失败: {str(e)}'
            }), 500

    @app.route('/api/v1/streams/optimized/stop', methods=['POST'])
    def stop_optimized_streams():
        """停止优化视频流"""
        try:
            optimized_service = get_optimized_multi_stream_service()
            if optimized_service:
                optimized_service.stop_all()

            return jsonify({
                'success': True,
                'message': '优化视频流已停止'
            })

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'停止优化流失败: {str(e)}'
            }), 500

    @app.route('/api/v1/streams', methods=['GET'])
    @app.route('/api/v1/streams/list', methods=['GET'])
    def get_all_streams():
        """获取所有视频流状态"""
        try:
            service = get_multi_stream_service()
            if not service:
                return jsonify({'success': False, 'message': '多路检测服务未初始化'}), 500
            
            status = service.get_all_status()
            return jsonify({'success': True, 'data': status, 'message': '获取视频流状态成功'})
            
        except Exception as e:
            logger.error(f"获取视频流状态异常: {e}")
            return jsonify({'success': False, 'message': f'获取视频流状态失败: {str(e)}'}), 500

    @app.route('/api/v1/streams', methods=['POST'])
    def add_stream():
        """添加视频流"""
        try:
            data = request.get_json()
            
            # 验证必需参数
            required_fields = ['stream_id', 'video_source']
            for field in required_fields:
                if field not in data:
                    return jsonify({'success': False, 'message': f'缺少必需参数: {field}'}), 400
            
            stream_id = data['stream_id']
            video_source = data['video_source']
            
            # 可选参数
            kwargs = {}
            if 'confidence_threshold' in data:
                kwargs['confidence_threshold'] = float(data['confidence_threshold'])
            if 'iou_threshold' in data:
                kwargs['iou_threshold'] = float(data['iou_threshold'])
            if 'max_fps' in data:
                kwargs['max_fps'] = int(data['max_fps'])
            
            service = get_multi_stream_service()
            if not service:
                return jsonify({'success': False, 'message': '多路检测服务未初始化'}), 500
            
            success = service.add_stream(stream_id, video_source, **kwargs)
            
            if success:
                return jsonify({
                    'success': True,
                    'data': {
                        'stream_id': stream_id,
                        'video_source': video_source,
                        'parameters': kwargs
                    },
                    'message': '视频流添加成功'
                })
            else:
                return jsonify({'success': False, 'message': '视频流添加失败'}), 400
            
        except Exception as e:
            logger.error(f"添加视频流异常: {e}")
            return jsonify({'success': False, 'message': f'添加视频流失败: {str(e)}'}), 500

    @app.route('/api/v1/streams/<stream_id>', methods=['DELETE'])
    def remove_stream(stream_id):
        """移除视频流"""
        try:
            service = get_multi_stream_service()
            if not service:
                return jsonify({'success': False, 'message': '多路检测服务未初始化'}), 500
            
            success = service.remove_stream(stream_id)
            
            if success:
                return jsonify({'success': True, 'message': f'视频流 {stream_id} 移除成功'})
            else:
                return jsonify({'success': False, 'message': '视频流不存在或移除失败'}), 404
            
        except Exception as e:
            logger.error(f"移除视频流异常: {e}")
            return jsonify({'success': False, 'message': f'移除视频流失败: {str(e)}'}), 500

    @app.route('/api/v1/streams/<stream_id>/start', methods=['POST'])
    def start_stream(stream_id):
        """启动指定视频流"""
        try:
            service = get_multi_stream_service()
            if not service:
                return jsonify({'success': False, 'message': '多路检测服务未初始化'}), 500
            
            success = service.start_stream(stream_id)
            
            if success:
                return jsonify({'success': True, 'message': f'视频流 {stream_id} 启动成功'})
            else:
                return jsonify({'success': False, 'message': '视频流启动失败'}), 400
            
        except Exception as e:
            logger.error(f"启动视频流异常: {e}")
            return jsonify({'success': False, 'message': f'启动视频流失败: {str(e)}'}), 500

    @app.route('/api/v1/streams/<stream_id>/stop', methods=['POST'])
    def stop_stream(stream_id):
        """停止指定视频流"""
        try:
            service = get_multi_stream_service()
            if not service:
                return jsonify({'success': False, 'message': '多路检测服务未初始化'}), 500
            
            success = service.stop_stream(stream_id)
            
            if success:
                return jsonify({'success': True, 'message': f'视频流 {stream_id} 停止成功'})
            else:
                return jsonify({'success': False, 'message': '视频流停止失败'}), 400
            
        except Exception as e:
            logger.error(f"停止视频流异常: {e}")
            return jsonify({'success': False, 'message': f'停止视频流失败: {str(e)}'}), 500

    @app.route('/api/v1/streams/start-all', methods=['POST'])
    def start_all_streams():
        """启动所有视频流"""
        try:
            service = get_multi_stream_service()
            if not service:
                return jsonify({'success': False, 'message': '多路检测服务未初始化'}), 500
            
            results = service.start_all_streams()
            
            success_count = sum(1 for success in results.values() if success)
            total_count = len(results)
            
            return jsonify({
                'success': True,
                'data': {
                    'results': results,
                    'success_count': success_count,
                    'total_count': total_count
                },
                'message': f'批量启动完成，成功 {success_count}/{total_count} 个流'
            })
            
        except Exception as e:
            logger.error(f"批量启动视频流异常: {e}")
            return jsonify({'success': False, 'message': f'批量启动视频流失败: {str(e)}'}), 500

    @app.route('/api/v1/streams/stop-all', methods=['POST'])
    def stop_all_streams():
        """停止所有视频流"""
        try:
            service = get_multi_stream_service()
            if not service:
                return jsonify({'success': False, 'message': '多路检测服务未初始化'}), 500
            
            results = service.stop_all_streams()
            
            success_count = sum(1 for success in results.values() if success)
            total_count = len(results)
            
            return jsonify({
                'success': True,
                'data': {
                    'results': results,
                    'success_count': success_count,
                    'total_count': total_count
                },
                'message': f'批量停止完成，成功 {success_count}/{total_count} 个流'
            })
            
        except Exception as e:
            logger.error(f"批量停止视频流异常: {e}")
            return jsonify({'success': False, 'message': f'批量停止视频流失败: {str(e)}'}), 500

    @app.route('/api/v1/streams/<stream_id>/result', methods=['GET'])
    def get_stream_result(stream_id):
        """获取指定视频流的检测结果"""
        try:
            service = get_multi_stream_service()
            if not service:
                return jsonify({'success': False, 'message': '多路检测服务未初始化'}), 500
            
            result = service.get_stream_result(stream_id)
            
            if result:
                return jsonify({'success': True, 'data': result, 'message': f'获取视频流 {stream_id} 检测结果成功'})
            else:
                return jsonify({'success': False, 'message': '视频流不存在或暂无检测结果'}), 404
            
        except Exception as e:
            logger.error(f"获取检测结果异常: {e}")
            return jsonify({'success': False, 'message': f'获取检测结果失败: {str(e)}'}), 500

    @app.route('/api/v1/streams/results', methods=['GET'])
    def get_all_results():
        """获取所有视频流的检测结果"""
        try:
            service = get_multi_stream_service()
            if not service:
                return jsonify({'success': False, 'message': '多路检测服务未初始化'}), 500
            
            results = service.get_all_results()
            
            return jsonify({
                'success': True,
                'data': {
                    'results': results,
                    'stream_count': len(results),
                    'timestamp': datetime.now().isoformat()
                },
                'message': '获取所有检测结果成功'
            })
            
        except Exception as e:
            logger.error(f"获取所有检测结果异常: {e}")
            return jsonify({'success': False, 'message': f'获取所有检测结果失败: {str(e)}'}), 500

    @app.route('/api/v1/streams/test', methods=['POST'])
    def test_multi_stream():
        """快速测试多路检测"""
        try:
            # 添加杭千高速测试流
            test_streams = [
                {
                    'stream_id': 'hangqian_highway_01',
                    'video_source': 'rtsp://127.0.0.1:554/live/test02',
                    'confidence_threshold': 0.25,
                    'max_fps': 15
                },
                {
                    'stream_id': 'hangqian_highway_02',
                    'video_source': 'rtsp://127.0.0.1:554/live/test',
                    'confidence_threshold': 0.25,
                    'max_fps': 15
                },
                {
                    'stream_id': 'hangqian_highway_03',
                    'video_source': 'rtsp://127.0.0.1:554/live/test01',
                    'confidence_threshold': 0.25,
                    'max_fps': 15
                }
            ]
            
            service = get_multi_stream_service()
            if not service:
                return jsonify({'success': False, 'message': '多路检测服务未初始化'}), 500
            
            results = []
            
            for stream_config in test_streams:
                stream_id = stream_config['stream_id']
                video_source = stream_config['video_source']
                
                # 添加流
                add_success = service.add_stream(
                    stream_id, 
                    video_source,
                    confidence_threshold=stream_config.get('confidence_threshold', 0.25),
                    max_fps=stream_config.get('max_fps', 10)
                )
                
                if add_success:
                    # 启动流
                    start_success = service.start_stream(stream_id)
                    results.append({
                        'stream_id': stream_id,
                        'add_success': add_success,
                        'start_success': start_success
                    })
                else:
                    results.append({
                        'stream_id': stream_id,
                        'add_success': add_success,
                        'start_success': False
                    })
            
            return jsonify({
                'success': True,
                'data': {
                    'test_results': results,
                    'message': '测试流配置完成，请查看检测结果'
                },
                'message': '多路检测测试启动成功'
            })
            
        except Exception as e:
            logger.error(f"测试多路检测异常: {e}")
            return jsonify({'success': False, 'message': f'测试多路检测失败: {str(e)}'}), 500

    @app.route('/api/v1/streams/statistics', methods=['GET'])
    def get_detection_statistics():
        """获取检测统计信息"""
        try:
            service = get_multi_stream_service()
            if not service:
                return jsonify({'success': False, 'message': '多路检测服务未初始化'}), 500
            
            all_status = service.get_all_status()
            all_results = service.get_all_results()
            
            # 统计信息
            total_streams = len(all_status)
            running_streams = sum(1 for status in all_status.values() if status and status['is_running'])
            total_detections = sum(result.get('total_detections', 0) for result in all_results.values())
            total_vehicles = sum(result.get('vehicle_count', 0) for result in all_results.values())
            
            # 平均FPS
            fps_values = [status['fps'] for status in all_status.values() if status and status['fps'] > 0]
            avg_fps = sum(fps_values) / len(fps_values) if fps_values else 0
            
            statistics = {
                'total_streams': total_streams,
                'running_streams': running_streams,
                'stopped_streams': total_streams - running_streams,
                'total_detections': total_detections,
                'total_vehicles': total_vehicles,
                'average_fps': round(avg_fps, 1),
                'timestamp': datetime.now().isoformat()
            }
            
            return jsonify({'success': True, 'data': statistics, 'message': '获取检测统计成功'})
            
        except Exception as e:
            logger.error(f"获取检测统计异常: {e}")
            return jsonify({'success': False, 'message': f'获取检测统计失败: {str(e)}'}), 500

    # ==================== 追踪API ====================

    @app.route('/api/v1/tracking/<stream_id>/single/init', methods=['POST'])
    def init_single_tracking(stream_id):
        """初始化单目标追踪"""
        try:
            data = request.get_json()
            bbox = data.get('bbox')  # [x, y, w, h]

            if not bbox or len(bbox) != 4:
                return jsonify({'success': False, 'message': '无效的边界框参数'}), 400

            # 获取当前帧
            service = get_multi_stream_service()
            result = service.get_stream_result(stream_id)

            if not result or 'latest_frame' not in result:
                return jsonify({'success': False, 'message': '无法获取视频帧'}), 400

            # 初始化追踪器
            tracking_service = get_enhanced_tracking_service()
            if stream_id not in tracking_service['single_trackers']:
                from services.enhanced_tracking_service import EnhancedSingleTracker
                tracking_service['single_trackers'][stream_id] = EnhancedSingleTracker()

            tracker = tracking_service['single_trackers'][stream_id]
            success = tracker.init_tracker(result['latest_frame'], tuple(bbox))

            if success:
                return jsonify({'success': True, 'message': '单目标追踪初始化成功'})
            else:
                return jsonify({'success': False, 'message': '单目标追踪初始化失败'}), 400

        except Exception as e:
            logger.error(f"初始化单目标追踪异常: {e}")
            return jsonify({'success': False, 'message': f'初始化失败: {str(e)}'}), 500

    @app.route('/api/v1/tracking/<stream_id>/multi/enable', methods=['POST'])
    def enable_multi_tracking(stream_id):
        """启用多目标追踪"""
        try:
            tracking_service = get_enhanced_tracking_service()

            if stream_id not in tracking_service['multi_trackers']:
                from services.enhanced_tracking_service import EnhancedMultiTracker
                tracking_service['multi_trackers'][stream_id] = EnhancedMultiTracker()

            return jsonify({'success': True, 'message': '多目标追踪已启用'})

        except Exception as e:
            logger.error(f"启用多目标追踪异常: {e}")
            return jsonify({'success': False, 'message': f'启用失败: {str(e)}'}), 500

    # ==================== 数据分析API ====================

    @app.route('/api/v1/analysis/traffic/statistics', methods=['GET'])
    def get_traffic_statistics():
        """获取交通统计数据"""
        try:
            stream_id = request.args.get('stream_id')
            days = request.args.get('days', 7, type=int)

            data_service = get_data_analysis_service()
            statistics = data_service.get_traffic_statistics(stream_id, days)

            return jsonify({'success': True, 'data': statistics, 'message': '获取交通统计成功'})

        except Exception as e:
            logger.error(f"获取交通统计异常: {e}")
            return jsonify({'success': False, 'message': f'获取交通统计失败: {str(e)}'}), 500

    @app.route('/api/v1/analysis/traffic/events', methods=['GET'])
    def get_traffic_events():
        """获取交通事件"""
        try:
            stream_id = request.args.get('stream_id')
            days = request.args.get('days', 7, type=int)

            data_service = get_data_analysis_service()
            events = data_service.get_traffic_events(stream_id, days)

            return jsonify({'success': True, 'data': events, 'message': '获取交通事件成功'})

        except Exception as e:
            logger.error(f"获取交通事件异常: {e}")
            return jsonify({'success': False, 'message': f'获取交通事件失败: {str(e)}'}), 500

    @app.route('/api/v1/analysis/traffic/patterns', methods=['GET'])
    def get_traffic_patterns():
        """获取交通模式分析"""
        try:
            stream_id = request.args.get('stream_id')

            data_service = get_data_analysis_service()
            patterns = data_service.analyze_traffic_patterns(stream_id)

            return jsonify({'success': True, 'data': patterns, 'message': '获取交通模式成功'})

        except Exception as e:
            logger.error(f"获取交通模式异常: {e}")
            return jsonify({'success': False, 'message': f'获取交通模式失败: {str(e)}'}), 500

    # ==================== QWEN分析API ====================

    @app.route('/api/v1/qwen/analyze/<stream_id>', methods=['POST'])
    def qwen_analyze_stream(stream_id):
        """QWEN分析指定视频流"""
        try:
            data = request.get_json()
            analysis_type = data.get('analysis_type', 'traffic_analysis')

            # 获取视频流结果
            service = get_multi_stream_service()
            result = service.get_stream_result(stream_id)

            if not result:
                return jsonify({'success': False, 'message': '无法获取视频流数据'}), 400

            # 从base64解码获取图像
            if 'frame_base64' not in result:
                return jsonify({'success': False, 'message': '无法获取视频帧'}), 400

            try:
                import base64
                import cv2
                import numpy as np

                # 解码base64图像
                image_data = base64.b64decode(result['frame_base64'])
                nparr = np.frombuffer(image_data, np.uint8)
                frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

                if frame is None:
                    return jsonify({'success': False, 'message': '图像解码失败'}), 400

                # QWEN分析
                qwen_service = get_qwen_analysis_service()
                analysis = qwen_service.analyze_traffic_image(
                    frame,
                    analysis_type,
                    result
                )

                return jsonify({'success': True, 'data': analysis, 'message': 'QWEN分析完成'})

            except Exception as decode_error:
                return jsonify({'success': False, 'message': f'图像处理失败: {str(decode_error)}'}), 400

        except Exception as e:
            logger.error(f"QWEN分析异常: {e}")
            return jsonify({'success': False, 'message': f'QWEN分析失败: {str(e)}'}), 500

    @app.route('/api/v1/qwen/batch-analyze', methods=['POST'])
    def qwen_batch_analyze():
        """QWEN批量分析所有视频流"""
        try:
            # 获取所有流的结果
            service = get_multi_stream_service()
            all_results = service.get_all_results()

            if not all_results:
                return jsonify({'success': False, 'message': '没有可分析的视频流'}), 400

            # 批量分析
            qwen_service = get_qwen_analysis_service()
            batch_analysis = qwen_service.batch_analyze_streams(all_results)

            return jsonify({'success': True, 'data': batch_analysis, 'message': 'QWEN批量分析完成'})

        except Exception as e:
            logger.error(f"QWEN批量分析异常: {e}")
            return jsonify({'success': False, 'message': f'QWEN批量分析失败: {str(e)}'}), 500

    @app.route('/api/v1/qwen/chat', methods=['POST'])
    def qwen_intelligent_chat():
        """QWEN智能对话分析"""
        try:
            data = request.get_json()
            user_query = data.get('query', '')

            if not user_query:
                return jsonify({
                    'success': False,
                    'message': '查询内容不能为空'
                }), 400

            qwen_service = get_qwen_analysis_service()
            if not qwen_service:
                return jsonify({
                    'success': False,
                    'message': 'QWEN分析服务未初始化'
                }), 500

            # 智能对话分析
            result = qwen_service.intelligent_chat_analysis(user_query)

            return jsonify({
                'success': True,
                'data': result,
                'message': '智能对话分析完成'
            })

        except Exception as e:
            logger.error(f"QWEN智能对话失败: {e}")
            return jsonify({
                'success': False,
                'message': f'智能对话失败: {str(e)}'
            }), 500

    @app.route('/api/v1/qwen/realtime-status', methods=['GET'])
    def get_qwen_realtime_status():
        """获取QWEN实时状况分析"""
        try:
            qwen_service = get_qwen_analysis_service()
            if not qwen_service:
                return jsonify({
                    'success': False,
                    'message': 'QWEN分析服务未初始化'
                }), 500

            # 获取实时状况
            status = qwen_service.get_realtime_status()

            return jsonify({
                'success': True,
                'data': status,
                'message': '实时状况获取成功'
            })

        except Exception as e:
            logger.error(f"获取QWEN实时状况失败: {e}")
            return jsonify({
                'success': False,
                'message': f'获取实时状况失败: {str(e)}'
            }), 500

    @app.route('/api/v1/qwen/update-data', methods=['POST'])
    def update_qwen_realtime_data():
        """更新QWEN实时数据"""
        try:
            data = request.get_json()
            data_type = data.get('type')
            value = data.get('value')

            if not data_type or value is None:
                return jsonify({
                    'success': False,
                    'message': '数据类型和值不能为空'
                }), 400

            qwen_service = get_qwen_analysis_service()
            if not qwen_service:
                return jsonify({
                    'success': False,
                    'message': 'QWEN分析服务未初始化'
                }), 500

            # 更新实时数据
            success = qwen_service.update_realtime_data(data_type, value)

            if success:
                return jsonify({
                    'success': True,
                    'message': f'数据更新成功: {data_type} = {value}'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '数据更新失败'
                }), 400

        except Exception as e:
            logger.error(f"更新QWEN实时数据失败: {e}")
            return jsonify({
                'success': False,
                'message': f'数据更新失败: {str(e)}'
            }), 500

    # ==================== 语音报警API ====================

    @app.route('/api/v1/voice-alert/trigger', methods=['POST'])
    def trigger_voice_alert():
        """触发语音报警"""
        try:
            data = request.get_json()
            alert_type = data.get('alert_type')
            level = data.get('level', 'medium')
            monitor_id = data.get('monitor_id')
            custom_message = data.get('custom_message')

            if not alert_type or not monitor_id:
                return jsonify({
                    'success': False,
                    'message': '缺少必需参数: alert_type, monitor_id'
                }), 400

            # 转换枚举类型
            try:
                alert_type_enum = AlertType(alert_type)
                level_enum = VoiceAlertLevel(level)
            except ValueError as e:
                return jsonify({
                    'success': False,
                    'message': f'无效的参数值: {str(e)}'
                }), 400

            voice_service = get_voice_alert_service()
            if not voice_service:
                return jsonify({
                    'success': False,
                    'message': '语音报警服务未初始化'
                }), 500

            success = voice_service.trigger_alert(
                alert_type_enum, level_enum, monitor_id, custom_message
            )

            if success:
                return jsonify({
                    'success': True,
                    'message': '语音报警已触发'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '语音报警触发失败（可能在冷却时间内）'
                })

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'触发语音报警失败: {str(e)}'
            }), 500

    @app.route('/api/v1/voice-alert/stop', methods=['POST'])
    def stop_voice_alert():
        """停止语音报警"""
        try:
            data = request.get_json()
            alert_type = data.get('alert_type', 'all')
            message = data.get('message', '停止语音报警')

            voice_service = get_voice_alert_service()
            if not voice_service:
                return jsonify({
                    'success': False,
                    'message': '语音报警服务未初始化'
                }), 500

            # 停止语音报警
            success = voice_service.stop_alert(alert_type)

            if success:
                return jsonify({
                    'success': True,
                    'message': f'语音报警已停止: {alert_type}',
                    'alert_type': alert_type
                }), 200
            else:
                return jsonify({
                    'success': False,
                    'message': '停止语音报警失败'
                }), 500

        except Exception as e:
            logger.error(f"停止语音报警异常: {e}")
            return jsonify({
                'success': False,
                'message': f'停止语音报警失败: {str(e)}'
            }), 500

    @app.route('/api/v1/voice-alert/history', methods=['GET'])
    def get_voice_alert_history():
        """获取语音报警历史"""
        try:
            limit = request.args.get('limit', 50, type=int)

            voice_service = get_voice_alert_service()
            if not voice_service:
                return jsonify({
                    'success': False,
                    'message': '语音报警服务未初始化'
                }), 500

            history = voice_service.get_alert_history(limit)

            return jsonify({
                'success': True,
                'data': history,
                'message': '获取语音报警历史成功'
            })

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取语音报警历史失败: {str(e)}'
            }), 500

    # ==================== 行人检测API ====================

    @app.route('/api/v1/pedestrian/enable/<stream_id>', methods=['POST'])
    def enable_pedestrian_detection(stream_id):
        """启用行人检测"""
        try:
            data = request.get_json() or {}

            # 获取行人检测服务
            pedestrian_service = get_pedestrian_detection_service()
            if not pedestrian_service:
                return jsonify({
                    'success': False,
                    'message': '行人检测服务未初始化'
                }), 500

            # 启用行人检测语音报警
            voice_service = get_voice_alert_service()
            if voice_service:
                voice_service.enable_alert_type(AlertType.PEDESTRIAN, stream_id)

                # 检查是否为测试模式
                if data.get('test_mode'):
                    voice_service.trigger_alert(
                        AlertType.PEDESTRIAN,
                        VoiceAlertLevel.MEDIUM,
                        stream_id,
                        "行人检测测试：检测到行人进入危险区域，请注意安全"
                    )

                    return jsonify({
                        'success': True,
                        'message': f'行人检测测试完成: {stream_id}',
                        'config': data
                    })

            # 正常模式：启动实际检测
            # 这里应该启动实际的行人检测逻辑
            # 暂时返回成功，实际实现需要根据具体需求
            return jsonify({
                'success': True,
                'message': f'行人检测已启用: {stream_id}',
                'config': data
            })

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'启用行人检测失败: {str(e)}'
            }), 500

    @app.route('/api/v1/pedestrian/disable/<stream_id>', methods=['POST'])
    def disable_pedestrian_detection_api(stream_id):
        """禁用行人检测"""
        try:
            # 获取语音报警服务并禁用行人检测报警
            voice_service = get_voice_alert_service()
            if voice_service:
                voice_service.disable_alert_type(AlertType.PEDESTRIAN, stream_id)
                print(f"✓ 已禁用行人检测语音报警: {stream_id}")

            # 获取行人检测服务
            pedestrian_service = get_pedestrian_detection_service()
            if not pedestrian_service:
                return jsonify({
                    'success': False,
                    'message': '行人检测服务未初始化'
                }), 500

            # 停止检测逻辑
            # 这里应该停止实际的行人检测

            return jsonify({
                'success': True,
                'message': f'行人检测已禁用: {stream_id}'
            })

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'禁用行人检测失败: {str(e)}'
            }), 500



    # ==================== 事故检测API ====================

    @app.route('/api/v1/accident/enable/<stream_id>', methods=['POST'])
    def enable_accident_detection(stream_id):
        """启用事故检测"""
        try:
            data = request.get_json() or {}
            return jsonify({
                'success': True,
                'message': f'事故检测已启用: {stream_id}',
                'config': data
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'启用事故检测失败: {str(e)}'
            }), 500

    @app.route('/api/v1/accident/disable/<stream_id>', methods=['POST'])
    def disable_accident_detection(stream_id):
        """禁用事故检测"""
        try:
            return jsonify({
                'success': True,
                'message': f'事故检测已禁用: {stream_id}'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'禁用事故检测失败: {str(e)}'
            }), 500

    # ==================== 违规检测API ====================

    @app.route('/api/v1/violation/enable/<stream_id>', methods=['POST'])
    def enable_violation_detection(stream_id):
        """启用违规检测"""
        try:
            data = request.get_json() or {}

            # 启用违规检测语音报警
            voice_service = get_voice_alert_service()
            if voice_service:
                voice_service.enable_alert_type(AlertType.VIOLATION, stream_id)

                # 检查是否为测试模式
                if data.get('test_mode'):
                    voice_service.trigger_alert(
                        AlertType.VIOLATION,
                        VoiceAlertLevel.HIGH,
                        stream_id,
                        "违规检测测试：检测到超速行为，请及时处理"
                    )

            return jsonify({
                'success': True,
                'message': f'违规检测已启用: {stream_id}',
                'config': data
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'启用违规检测失败: {str(e)}'
            }), 500

    @app.route('/api/v1/violation/disable/<stream_id>', methods=['POST'])
    def disable_violation_detection(stream_id):
        """禁用违规检测"""
        try:
            # 禁用违规检测语音报警
            voice_service = get_voice_alert_service()
            if voice_service:
                voice_service.disable_alert_type(AlertType.VIOLATION, stream_id)
                print(f"✓ 已禁用违规检测语音报警: {stream_id}")

            return jsonify({
                'success': True,
                'message': f'违规检测已禁用: {stream_id}'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'禁用违规检测失败: {str(e)}'
            }), 500

    # ==================== 拥堵分析API ====================

    @app.route('/api/v1/congestion/enable/<stream_id>', methods=['POST'])
    def enable_congestion_analysis(stream_id):
        """启用拥堵分析"""
        try:
            data = request.get_json() or {}

            # 启用拥堵分析语音报警
            voice_service = get_voice_alert_service()
            if voice_service:
                voice_service.enable_alert_type(AlertType.CONGESTION, stream_id)

                # 检查是否为测试模式
                if data.get('test_mode'):
                    voice_service.trigger_alert(
                        AlertType.CONGESTION,
                        VoiceAlertLevel.MEDIUM,
                        stream_id,
                        "拥堵分析测试：检测到交通拥堵，建议优化信号配时"
                    )

            return jsonify({
                'success': True,
                'message': f'拥堵分析已启用: {stream_id}',
                'config': data
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'启用拥堵分析失败: {str(e)}'
            }), 500

    @app.route('/api/v1/congestion/disable/<stream_id>', methods=['POST'])
    def disable_congestion_analysis(stream_id):
        """禁用拥堵分析"""
        try:
            # 禁用拥堵分析语音报警
            voice_service = get_voice_alert_service()
            if voice_service:
                voice_service.disable_alert_type(AlertType.CONGESTION, stream_id)
                print(f"✓ 已禁用拥堵分析语音报警: {stream_id}")

            return jsonify({
                'success': True,
                'message': f'拥堵分析已禁用: {stream_id}'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'禁用拥堵分析失败: {str(e)}'
            }), 500

    # ==================== 拥堵预警API ====================

    @app.route('/api/v1/congestion-warning/active', methods=['GET'])
    def get_active_warnings():
        """获取活跃的拥堵预警"""
        try:
            warning_service = get_congestion_warning_service()
            if not warning_service:
                return jsonify({
                    'success': False,
                    'message': '拥堵预警服务未初始化'
                }), 500

            warnings = warning_service.get_active_warnings()

            # 转换为JSON格式
            warning_data = []
            for warning in warnings:
                warning_data.append({
                    'id': warning.id,
                    'stream_id': warning.stream_id,
                    'warning_level': warning.warning_level.value,
                    'predicted_congestion_level': warning.predicted_congestion_level,
                    'prediction_time': warning.prediction_time.isoformat(),
                    'confidence': warning.confidence,
                    'estimated_duration': warning.estimated_duration,
                    'affected_area': warning.affected_area,
                    'recommendations': warning.recommendations,
                    'timestamp': warning.timestamp.isoformat()
                })

            return jsonify({
                'success': True,
                'data': {
                    'warnings': warning_data,
                    'count': len(warning_data)
                },
                'message': '获取活跃预警成功'
            })

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取活跃预警失败: {str(e)}'
            }), 500

    @app.route('/api/v1/congestion-warning/history', methods=['GET'])
    def get_warning_history():
        """获取拥堵预警历史"""
        try:
            limit = request.args.get('limit', 50, type=int)

            warning_service = get_congestion_warning_service()
            if not warning_service:
                return jsonify({
                    'success': False,
                    'message': '拥堵预警服务未初始化'
                }), 500

            warnings = warning_service.get_warning_history(limit)

            # 转换为JSON格式
            warning_data = []
            for warning in warnings:
                warning_data.append({
                    'id': warning.id,
                    'stream_id': warning.stream_id,
                    'warning_level': warning.warning_level.value,
                    'predicted_congestion_level': warning.predicted_congestion_level,
                    'prediction_time': warning.prediction_time.isoformat(),
                    'confidence': warning.confidence,
                    'estimated_duration': warning.estimated_duration,
                    'affected_area': warning.affected_area,
                    'recommendations': warning.recommendations,
                    'timestamp': warning.timestamp.isoformat()
                })

            return jsonify({
                'success': True,
                'data': {
                    'warnings': warning_data,
                    'count': len(warning_data)
                },
                'message': '获取预警历史成功'
            })

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取预警历史失败: {str(e)}'
            }), 500

    @app.route('/api/v1/congestion-warning/stream/<stream_id>', methods=['GET'])
    def get_stream_warning(stream_id):
        """获取指定流的拥堵预警"""
        try:
            warning_service = get_congestion_warning_service()
            if not warning_service:
                return jsonify({
                    'success': False,
                    'message': '拥堵预警服务未初始化'
                }), 500

            warning = warning_service.get_stream_warning(stream_id)

            if warning:
                warning_data = {
                    'id': warning.id,
                    'stream_id': warning.stream_id,
                    'warning_level': warning.warning_level.value,
                    'predicted_congestion_level': warning.predicted_congestion_level,
                    'prediction_time': warning.prediction_time.isoformat(),
                    'confidence': warning.confidence,
                    'estimated_duration': warning.estimated_duration,
                    'affected_area': warning.affected_area,
                    'recommendations': warning.recommendations,
                    'timestamp': warning.timestamp.isoformat()
                }

                return jsonify({
                    'success': True,
                    'data': warning_data,
                    'message': '获取流预警成功'
                })
            else:
                return jsonify({
                    'success': True,
                    'data': None,
                    'message': '该流暂无活跃预警'
                })

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取流预警失败: {str(e)}'
            }), 500

    return app

if __name__ == '__main__':
    print("🚀 启动多路视频检测系统...")
    app = create_app()

    print("✓ 系统启动成功！")
    print("📱 测试页面: http://127.0.0.1:5500/multi_stream_test.html")
    print("🔗 API文档: http://127.0.0.1:5500/")
    print("🌐 WebSocket支持已启用")

    # 使用SocketIO运行应用
    app.socketio.run(
        app,
        host='0.0.0.0',
        port=5500,
        debug=True,
        allow_unsafe_werkzeug=True
    )
