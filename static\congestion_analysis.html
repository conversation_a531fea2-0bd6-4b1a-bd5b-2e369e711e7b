<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拥堵分析系统 - 高速公路YOLO智能监控</title>
    
    <!-- 本地依赖 -->
    <link rel="stylesheet" href="/static/arco.css">
    <script src="/static/chart.min.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f7f8fa;
            color: #1d2129;
        }
        
        /* 顶部导航栏 */
        .top-navbar {
            background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
            color: white;
            padding: 0 24px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .navbar-left {
            display: flex;
            align-items: center;
        }
        
        .system-logo {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 20px;
            background-image: url('/static/logo.webp');
            background-size: cover;
            background-position: center;
        }
        
        .system-name {
            font-size: 20px;
            font-weight: 600;
        }
        
        .back-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.3s;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        /* 主内容区 */
        .main-content {
            padding: 24px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        /* 页面标题 */
        .page-header {
            margin-bottom: 24px;
        }
        
        .page-title {
            font-size: 22px;
            font-weight: 600;
            color: #1d2129;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .page-subtitle {
            color: #86909c;
            font-size: 16px;
        }
        
        /* 拥堵等级指示器 */
        .congestion-levels {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid #e5e6eb;
        }
        
        .levels-header {
            font-size: 18px;
            font-weight: 600;
            color: #1d2129;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .levels-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }
        
        .level-card {
            background: #f7f8fa;
            border: 2px solid #e5e6eb;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
            transition: all 0.3s;
        }
        
        .level-card.smooth {
            border-color: #00b42a;
            background: #f6ffed;
        }
        
        .level-card.slow {
            border-color: #faad14;
            background: #fff7e6;
        }
        
        .level-card.congested {
            border-color: #ff7875;
            background: #fff2f0;
        }
        
        .level-card.blocked {
            border-color: #f53f3f;
            background: #fff1f0;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }
        
        .level-icon {
            font-size: 32px;
            margin-bottom: 8px;
        }
        
        .level-name {
            font-weight: 600;
            color: #1d2129;
            margin-bottom: 4px;
        }
        
        .level-desc {
            font-size: 12px;
            color: #86909c;
            margin-bottom: 8px;
        }
        
        .level-count {
            font-size: 20px;
            font-weight: 600;
            color: #1d2129;
        }
        
        /* 控制面板 */
        .control-panel {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid #e5e6eb;
        }
        
        .control-header {
            font-size: 18px;
            font-weight: 600;
            color: #1d2129;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .control-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .control-label {
            font-size: 14px;
            font-weight: 500;
            color: #4e5969;
        }
        
        .control-input {
            padding: 8px 12px;
            border: 1px solid #e5e6eb;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .control-input:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
        }
        
        .control-buttons {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #1890ff, #096dd9);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .btn-success {
            background: #00b42a;
            color: white;
        }
        
        .btn-warning {
            background: #faad14;
            color: white;
        }
        
        .btn-danger {
            background: #f53f3f;
            color: white;
        }
        
        /* 分析网格 */
        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 24px;
            margin-bottom: 24px;
        }
        
        .analysis-panel {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid #e5e6eb;
            position: relative;
        }
        
        .analysis-panel.congested {
            border-color: #f53f3f;
            box-shadow: 0 4px 16px rgba(245, 63, 63, 0.2);
        }
        
        .panel-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e5e6eb;
        }
        
        .panel-title {
            font-size: 16px;
            font-weight: 600;
            color: #1d2129;
        }
        
        .panel-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-smooth {
            background: #f6ffed;
            color: #00b42a;
        }
        
        .status-slow {
            background: #fff7e6;
            color: #faad14;
        }
        
        .status-congested {
            background: #fff2f0;
            color: #ff7875;
        }
        
        .status-blocked {
            background: #fff1f0;
            color: #f53f3f;
            animation: pulse 2s infinite;
        }
        
        .video-display {
            width: 100%;
            height: 280px;
            background: #000;
            border-radius: 8px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            position: relative;
            overflow: hidden;
        }
        
        .congestion-overlay {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(245, 63, 63, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            display: none;
        }
        
        .analysis-info {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .info-item {
            background: #f7f8fa;
            padding: 12px;
            border-radius: 6px;
            text-align: center;
        }
        
        .info-value {
            font-size: 18px;
            font-weight: 600;
            color: #1d2129;
            margin-bottom: 4px;
        }
        
        .info-label {
            font-size: 12px;
            color: #86909c;
        }
        
        .panel-controls {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }
        
        /* 图表区域 */
        .chart-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 24px;
            margin-bottom: 24px;
            min-height: 700px;
        }
        
        .chart-panel {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid #e5e6eb;
        }
        
        .chart-header {
            font-size: 18px;
            font-weight: 600;
            color: #1d2129;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
        }
        
        /* 预警面板 */
        .alert-panel {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid #e5e6eb;
        }
        
        .alert-header {
            font-size: 18px;
            font-weight: 600;
            color: #1d2129;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .alert-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .alert-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 8px;
            border-left: 4px solid;
        }
        
        .alert-high {
            background: #fff2f0;
            border-left-color: #f53f3f;
        }
        
        .alert-medium {
            background: #fff7e6;
            border-left-color: #faad14;
        }
        
        .alert-low {
            background: #f6ffed;
            border-left-color: #00b42a;
        }
        
        .alert-icon {
            margin-right: 12px;
            font-size: 18px;
        }
        
        .alert-content {
            flex: 1;
        }
        
        .alert-title {
            font-weight: 500;
            color: #1d2129;
            margin-bottom: 4px;
        }
        
        .alert-time {
            font-size: 12px;
            color: #86909c;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .analysis-grid {
                grid-template-columns: 1fr;
            }
            
            .chart-grid {
                grid-template-columns: 1fr;
            }
            
            .control-grid {
                grid-template-columns: 1fr;
            }
            
            .levels-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="top-navbar">
        <div class="navbar-left">
            <div class="system-logo"></div>
            <div class="system-name">拥堵分析系统</div>
        </div>
        <a href="/static/dashboard.html" class="back-btn">
            <span>←</span>
            <span>返回控制台</span>
        </a>
    </nav>
    
    <!-- 主内容区 -->
    <main class="main-content">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">
                <span>🚦</span>
                <span>拥堵分析系统</span>
            </h1>
            <p class="page-subtitle">高速公路交通拥堵智能分析与预测系统</p>
        </div>
        
        <!-- 拥堵等级指示器 -->
        <div class="congestion-levels">
            <div class="levels-header">
                <span>📊</span>
                <span>拥堵等级分布</span>
            </div>
            
            <div class="levels-grid">
                <div class="level-card smooth" id="smoothCard">
                    <div class="level-icon">🟢</div>
                    <div class="level-name">畅通</div>
                    <div class="level-desc">车速 > 80 km/h</div>
                    <div class="level-count" id="smoothCount">0</div>
                </div>
                
                <div class="level-card slow" id="slowCard">
                    <div class="level-icon">🟡</div>
                    <div class="level-name">缓慢</div>
                    <div class="level-desc">50-80 km/h</div>
                    <div class="level-count" id="slowCount">0</div>
                </div>
                
                <div class="level-card congested" id="congestedCard">
                    <div class="level-icon">🟠</div>
                    <div class="level-name">拥堵</div>
                    <div class="level-desc">20-50 km/h</div>
                    <div class="level-count" id="congestedCount">0</div>
                </div>
                
                <div class="level-card blocked" id="blockedCard">
                    <div class="level-icon">🔴</div>
                    <div class="level-name">严重拥堵</div>
                    <div class="level-desc">< 20 km/h</div>
                    <div class="level-count" id="blockedCount">0</div>
                </div>
            </div>
        </div>
        
        <!-- 控制面板 -->
        <div class="control-panel">
            <div class="control-header">
                <span>🎛️</span>
                <span>分析控制</span>
            </div>
            
            <div class="control-grid">
                <div class="control-group">
                    <label class="control-label">分析模式</label>
                    <select class="control-input" id="analysisMode">
                        <option value="realtime">实时分析</option>
                        <option value="predictive" selected>预测分析</option>
                        <option value="historical">历史分析</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label class="control-label">预测时长(分钟)</label>
                    <input type="number" class="control-input" id="predictTime" value="30" min="5" max="120">
                </div>
                
                <div class="control-group">
                    <label class="control-label">拥堵阈值</label>
                    <input type="number" class="control-input" id="congestionThreshold" value="30" min="10" max="80">
                </div>
                
                <div class="control-group">
                    <label class="control-label">更新间隔(秒)</label>
                    <input type="number" class="control-input" id="updateInterval" value="10" min="5" max="60">
                </div>
            </div>
            
            <div class="control-buttons">
                <button class="btn btn-primary" id="startAnalysisBtn">
                    <span>▶️</span>
                    <span>启动分析</span>
                </button>
                <button class="btn btn-warning" id="pauseAnalysisBtn">
                    <span>⏸️</span>
                    <span>暂停分析</span>
                </button>
                <button class="btn btn-danger" id="stopAnalysisBtn">
                    <span>⏹️</span>
                    <span>停止分析</span>
                </button>
                <button class="btn btn-success" id="refreshStreamsBtn">
                    <span>🔄</span>
                    <span>刷新数据</span>
                </button>
            </div>
        </div>
        
        <!-- 分析网格 -->
        <div id="analysisGrid" class="analysis-grid">
            <!-- 动态生成分析面板 -->
        </div>
        
        <!-- 图表区域 -->
        <div class="chart-grid">
            <div class="chart-panel">
                <div class="chart-header">
                    <span>📈</span>
                    <span>拥堵趋势分析</span>
                </div>
                <div class="chart-container">
                    <canvas id="congestionTrendChart"></canvas>
                </div>
            </div>
            
            <div class="chart-panel">
                <div class="chart-header">
                    <span>🕐</span>
                    <span>24小时拥堵分布</span>
                </div>
                <div class="chart-container">
                    <canvas id="hourlyDistributionChart"></canvas>
                </div>
            </div>
            
            <div class="chart-panel">
                <div class="chart-header">
                    <span>🗺️</span>
                    <span>路段拥堵对比</span>
                </div>
                <div class="chart-container">
                    <canvas id="sectionComparisonChart"></canvas>
                </div>
            </div>
            
            <div class="chart-panel">
                <div class="chart-header">
                    <span>🔮</span>
                    <span>拥堵预测</span>
                </div>
                <div class="chart-container">
                    <canvas id="predictionChart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- 预警面板 -->
        <div class="alert-panel">
            <div class="alert-header">
                <span>⚠️</span>
                <span>拥堵预警</span>
            </div>
            
            <div id="alertList" class="alert-list">
                <div style="text-align: center; color: #86909c; padding: 40px;">
                    暂无拥堵预警
                </div>
            </div>
        </div>
    </main>

    <script>
        // 全局变量
        let streams = {};
        let analysisActive = false;
        let updateInterval = null;
        let charts = {};
        let congestionData = {
            smooth: 0,
            slow: 0,
            congested: 0,
            blocked: 0
        };
        let alerts = [];
        
        // DOM元素
        const analysisGrid = document.getElementById('analysisGrid');
        const alertList = document.getElementById('alertList');
        
        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            refreshStreams();
            setupEventListeners();
            initCharts();
            startRealTimeUpdate();
        });
        
        // 设置事件监听器
        function setupEventListeners() {
            document.getElementById('startAnalysisBtn').addEventListener('click', startAnalysis);
            document.getElementById('pauseAnalysisBtn').addEventListener('click', pauseAnalysis);
            document.getElementById('stopAnalysisBtn').addEventListener('click', stopAnalysis);
            document.getElementById('refreshStreamsBtn').addEventListener('click', refreshStreams);
        }
        
        // 初始化图表
        function initCharts() {
            // 拥堵趋势图
            const trendCtx = document.getElementById('congestionTrendChart').getContext('2d');
            charts.trend = new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '平均拥堵指数',
                        data: [],
                        borderColor: '#1890ff',
                        backgroundColor: 'rgba(24, 144, 255, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: {
                                color: '#f0f0f0'
                            }
                        },
                        x: {
                            grid: {
                                color: '#f0f0f0'
                            }
                        }
                    }
                }
            });
            
            // 24小时分布图
            const hourlyCtx = document.getElementById('hourlyDistributionChart').getContext('2d');
            charts.hourly = new Chart(hourlyCtx, {
                type: 'bar',
                data: {
                    labels: Array.from({length: 24}, (_, i) => `${i}:00`),
                    datasets: [{
                        label: '拥堵指数',
                        data: [],
                        backgroundColor: '#1890ff',
                        borderColor: '#096dd9',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
            
            // 路段对比图
            const sectionCtx = document.getElementById('sectionComparisonChart').getContext('2d');
            charts.section = new Chart(sectionCtx, {
                type: 'radar',
                data: {
                    labels: [],
                    datasets: [{
                        label: '拥堵指数',
                        data: [],
                        borderColor: '#1890ff',
                        backgroundColor: 'rgba(24, 144, 255, 0.2)',
                        pointBackgroundColor: '#1890ff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
            
            // 预测图
            const predictionCtx = document.getElementById('predictionChart').getContext('2d');
            charts.prediction = new Chart(predictionCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '当前',
                        data: [],
                        borderColor: '#1890ff',
                        backgroundColor: 'rgba(24, 144, 255, 0.1)'
                    }, {
                        label: '预测',
                        data: [],
                        borderColor: '#faad14',
                        backgroundColor: 'rgba(250, 173, 20, 0.1)',
                        borderDash: [5, 5]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        }
        
        // API请求
        async function apiRequest(url, options = {}) {
            try {
                const response = await fetch(`http://127.0.0.1:5500/api/v1${url}`, {
                    headers: { 'Content-Type': 'application/json' },
                    ...options
                });
                const result = await response.json();
                return result;
            } catch (error) {
                console.error('API请求失败:', error);
                return { success: false, message: error.message };
            }
        }
        
        // 刷新视频流
        async function refreshStreams() {
            const result = await apiRequest('/streams');
            
            if (result.success) {
                streams = result.data;
                updateAnalysisGrid();
                updateCongestionLevels();
                console.log(`刷新成功，共 ${Object.keys(streams).length} 个流`);
            } else {
                console.error(`刷新失败: ${result.message}`);
            }
        }
        
        // 更新分析网格
        function updateAnalysisGrid() {
            analysisGrid.innerHTML = '';
            
            for (const [streamId, status] of Object.entries(streams)) {
                if (status.is_running) {
                    const panel = createAnalysisPanel(streamId, status);
                    analysisGrid.appendChild(panel);
                }
            }
            
            // 获取分析结果
            updateAnalysisResults();
        }
        
        // 创建分析面板
        function createAnalysisPanel(streamId, status) {
            const panel = document.createElement('div');
            panel.className = 'analysis-panel';
            panel.id = `analysis-panel-${streamId}`;
            
            const streamName = getStreamName(streamId);
            
            panel.innerHTML = `
                <div class="panel-header">
                    <div class="panel-title">${streamName}</div>
                    <div class="panel-status status-smooth" id="status-${streamId}">畅通</div>
                </div>
                
                <div class="video-display" id="display-${streamId}" style="position: relative;">
                    <video id="video-${streamId}" width="100%" height="280" style="border-radius: 8px; background: #000;" autoplay muted>
                        <source src="" type="video/mp4">
                        您的浏览器不支持视频播放
                    </video>
                    <canvas id="canvas-${streamId}" width="100%" height="280" style="position: absolute; top: 0; left: 0; border-radius: 8px; display: none;"></canvas>
                    <div class="congestion-overlay" id="overlay-${streamId}" style="position: absolute; top: 10px; left: 10px; background: rgba(255,165,0,0.8); color: white; padding: 5px 10px; border-radius: 4px; display: none;">🚦 拥堵检测</div>
                </div>
                
                <div class="analysis-info">
                    <div class="info-item">
                        <div class="info-value" id="vehicles-${streamId}">0</div>
                        <div class="info-label">车辆数量</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value" id="speed-${streamId}">0</div>
                        <div class="info-label">平均速度</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value" id="density-${streamId}">0</div>
                        <div class="info-label">交通密度</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value" id="congestion-${streamId}">0</div>
                        <div class="info-label">拥堵指数</div>
                    </div>
                </div>
                
                <div class="panel-controls">
                    <button class="btn btn-primary btn-small" onclick="enableCongestionAnalysis('${streamId}')">
                        启用分析
                    </button>
                    <button class="btn btn-warning btn-small" onclick="disableCongestionAnalysis('${streamId}')">
                        禁用分析
                    </button>
                    <button class="btn btn-danger btn-small" onclick="simulateCongestion('${streamId}')">
                        模拟拥堵
                    </button>
                </div>
            `;
            
            return panel;
        }
        
        // 启动分析
        async function startAnalysis() {
            analysisActive = true;
            updateButtonStates();
            
            // 为所有流启用拥堵分析
            for (const streamId of Object.keys(streams)) {
                await enableCongestionAnalysis(streamId);
            }
            
            addAlert('系统启动', '拥堵分析系统已启动', 'low');
        }
        
        // 暂停分析
        function pauseAnalysis() {
            analysisActive = false;
            updateButtonStates();
            addAlert('系统暂停', '拥堵分析系统已暂停', 'medium');
        }
        
        // 停止分析
        async function stopAnalysis() {
            analysisActive = false;
            updateButtonStates();

            // 停止语音报警
            await stopVoiceAlert();

            // 为所有流禁用拥堵分析
            for (const streamId of Object.keys(streams)) {
                await disableCongestionAnalysis(streamId);
            }

            addAlert('系统停止', '拥堵分析系统已停止', 'medium');
        }

        // 停止语音报警
        async function stopVoiceAlert() {
            try {
                // 1. 停止前端语音播放（如果有）
                stopFrontendVoice();

                // 2. 停止后端语音播放
                const result = await apiRequest('/voice-alert/stop', {
                    method: 'POST',
                    body: JSON.stringify({
                        alert_type: 'congestion',
                        message: '停止拥堵分析语音报警'
                    })
                });

                if (result.success) {
                    console.log('✅ 拥堵分析语音报警已停止');
                } else {
                    console.error('❌ 停止拥堵分析语音报警失败:', result.message);
                }
            } catch (error) {
                console.error('❌ 停止拥堵分析语音报警异常:', error);
            }
        }

        // 停止前端语音播放
        function stopFrontendVoice() {
            try {
                // 停止Web Speech API
                if (window.speechSynthesis) {
                    window.speechSynthesis.cancel();
                    console.log('✅ 已停止Web Speech API语音');
                }

                // 停止所有audio元素
                const audioElements = document.querySelectorAll('audio');
                audioElements.forEach(audio => {
                    audio.pause();
                    audio.currentTime = 0;
                });

                if (audioElements.length > 0) {
                    console.log(`✅ 已停止 ${audioElements.length} 个音频元素`);
                }

            } catch (error) {
                console.error('❌ 停止前端语音失败:', error);
            }
        }
        
        // 启用拥堵分析
        async function enableCongestionAnalysis(streamId) {
            const result = await apiRequest(`/congestion/enable/${streamId}`, {
                method: 'POST',
                body: JSON.stringify({
                    mode: document.getElementById('analysisMode').value,
                    predict_time: parseInt(document.getElementById('predictTime').value),
                    threshold: parseInt(document.getElementById('congestionThreshold').value),
                    update_interval: parseInt(document.getElementById('updateInterval').value)
                })
            });
            
            if (result.success) {
                console.log(`拥堵分析启用成功: ${streamId}`);
            } else {
                console.error(`拥堵分析启用失败: ${result.message}`);
                addAlert('启用失败', `${getStreamName(streamId)} 拥堵分析启用失败`, 'high');
            }
        }
        
        // 禁用拥堵分析
        async function disableCongestionAnalysis(streamId) {
            const result = await apiRequest(`/congestion/disable/${streamId}`, {
                method: 'POST'
            });
            
            if (result.success) {
                console.log(`拥堵分析禁用成功: ${streamId}`);
            } else {
                console.error(`拥堵分析禁用失败: ${result.message}`);
            }
        }
        
        // 模拟拥堵
        function simulateCongestion(streamId) {
            const congestionLevels = ['smooth', 'slow', 'congested', 'blocked'];
            const randomLevel = congestionLevels[Math.floor(Math.random() * congestionLevels.length)];
            
            const streamName = getStreamName(streamId);
            updatePanelStatus(streamId, randomLevel);
            
            // 显示拥堵覆盖层
            const overlay = document.getElementById(`overlay-${streamId}`);
            if (overlay && randomLevel !== 'smooth') {
                overlay.style.display = 'block';
            }
            
            // 添加预警
            if (randomLevel === 'congested' || randomLevel === 'blocked') {
                const severity = randomLevel === 'blocked' ? 'high' : 'medium';
                addAlert('拥堵预警', `${streamName} 检测到${getLevelName(randomLevel)}`, severity);
            }
            
            // 5秒后恢复
            setTimeout(() => {
                updatePanelStatus(streamId, 'smooth');
                if (overlay) {
                    overlay.style.display = 'none';
                }
            }, 5000);
        }
        
        // 更新面板状态
        function updatePanelStatus(streamId, level) {
            const statusEl = document.getElementById(`status-${streamId}`);
            const panel = document.getElementById(`analysis-panel-${streamId}`);
            const congestionEl = document.getElementById(`congestion-${streamId}`);
            
            const levelNames = {
                smooth: '畅通',
                slow: '缓慢',
                congested: '拥堵',
                blocked: '严重拥堵'
            };
            
            const congestionIndex = {
                smooth: Math.floor(Math.random() * 20),
                slow: Math.floor(Math.random() * 30) + 20,
                congested: Math.floor(Math.random() * 30) + 50,
                blocked: Math.floor(Math.random() * 20) + 80
            };
            
            if (statusEl) {
                statusEl.textContent = levelNames[level];
                statusEl.className = `panel-status status-${level}`;
            }
            
            if (panel) {
                panel.className = `analysis-panel ${level === 'congested' || level === 'blocked' ? 'congested' : ''}`;
            }
            
            if (congestionEl) {
                congestionEl.textContent = congestionIndex[level];
            }
            
            // 更新拥堵等级统计
            updateCongestionStats(level);
        }
        
        // 更新拥堵统计
        function updateCongestionStats(level) {
            // 重置统计
            congestionData = { smooth: 0, slow: 0, congested: 0, blocked: 0 };
            
            // 统计当前所有面板的状态
            Object.keys(streams).forEach(streamId => {
                const statusEl = document.getElementById(`status-${streamId}`);
                if (statusEl) {
                    const statusText = statusEl.textContent;
                    if (statusText === '畅通') congestionData.smooth++;
                    else if (statusText === '缓慢') congestionData.slow++;
                    else if (statusText === '拥堵') congestionData.congested++;
                    else if (statusText === '严重拥堵') congestionData.blocked++;
                }
            });
            
            updateCongestionLevels();
        }
        
        // 更新拥堵等级显示
        function updateCongestionLevels() {
            document.getElementById('smoothCount').textContent = congestionData.smooth;
            document.getElementById('slowCount').textContent = congestionData.slow;
            document.getElementById('congestedCount').textContent = congestionData.congested;
            document.getElementById('blockedCount').textContent = congestionData.blocked;
        }
        
        // 更新按钮状态
        function updateButtonStates() {
            const startBtn = document.getElementById('startAnalysisBtn');
            const pauseBtn = document.getElementById('pauseAnalysisBtn');
            const stopBtn = document.getElementById('stopAnalysisBtn');
            
            if (analysisActive) {
                startBtn.disabled = true;
                pauseBtn.disabled = false;
                stopBtn.disabled = false;
            } else {
                startBtn.disabled = false;
                pauseBtn.disabled = true;
                stopBtn.disabled = true;
            }
        }
        
        // 更新分析结果
        async function updateAnalysisResults() {
            const result = await apiRequest('/streams/results');
            
            if (result.success && result.data.results) {
                for (const [streamId, detection] of Object.entries(result.data.results)) {
                    updateStreamDisplay(streamId, detection);
                    
                    // 模拟拥堵分析结果
                    if (analysisActive) {
                        simulateCongestionAnalysis(streamId, detection);
                    }
                }
            }
            
            // 更新图表
            updateCharts();
        }
        
        // 更新流显示
        function updateStreamDisplay(streamId, detection) {
            const display = document.getElementById(`display-${streamId}`);
            
            if (detection.frame_base64 && display) {
                display.innerHTML = `
                    <div class="congestion-overlay" id="overlay-${streamId}" style="display: none;">🚦 拥堵检测</div>
                    <img src="data:image/jpeg;base64,${detection.frame_base64}" 
                         style="width:100%;height:100%;object-fit:contain;">
                `;
            }
            
            // 更新分析信息
            updateAnalysisInfo(streamId, detection);
        }
        
        // 更新分析信息
        function updateAnalysisInfo(streamId, detection) {
            const vehiclesEl = document.getElementById(`vehicles-${streamId}`);
            const speedEl = document.getElementById(`speed-${streamId}`);
            const densityEl = document.getElementById(`density-${streamId}`);
            
            if (vehiclesEl) vehiclesEl.textContent = detection.total_detections || 0;
            if (speedEl) speedEl.textContent = Math.floor(Math.random() * 60) + 40 + ' km/h';
            if (densityEl) densityEl.textContent = (Math.random() * 3 + 0.5).toFixed(1);
        }
        
        // 模拟拥堵分析
        function simulateCongestionAnalysis(streamId, detection) {
            // 随机生成拥堵分析结果
            if (Math.random() < 0.1) { // 10% 概率检测到拥堵变化
                const levels = ['smooth', 'slow', 'congested', 'blocked'];
                const weights = [0.4, 0.3, 0.2, 0.1]; // 权重分布
                const randomLevel = levels[weightedRandom(weights)];
                
                updatePanelStatus(streamId, randomLevel);
                
                if (randomLevel === 'congested' || randomLevel === 'blocked') {
                    const streamName = getStreamName(streamId);
                    const severity = randomLevel === 'blocked' ? 'high' : 'medium';
                    addAlert('拥堵预警', `${streamName} 检测到${getLevelName(randomLevel)}`, severity);
                }
            }
        }
        
        // 权重随机选择
        function weightedRandom(weights) {
            const random = Math.random();
            let sum = 0;
            for (let i = 0; i < weights.length; i++) {
                sum += weights[i];
                if (random < sum) return i;
            }
            return weights.length - 1;
        }
        
        // 获取等级名称
        function getLevelName(level) {
            const names = {
                smooth: '畅通',
                slow: '缓慢',
                congested: '拥堵',
                blocked: '严重拥堵'
            };
            return names[level] || level;
        }
        
        // 更新图表
        function updateCharts() {
            // 更新趋势图
            const now = new Date();
            const timeLabels = [];
            const trendData = [];
            
            for (let i = 9; i >= 0; i--) {
                const time = new Date(now.getTime() - i * 60000);
                timeLabels.push(time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }));
                trendData.push(Math.floor(Math.random() * 60) + 20);
            }
            
            charts.trend.data.labels = timeLabels;
            charts.trend.data.datasets[0].data = trendData;
            charts.trend.update();
            
            // 更新24小时分布图
            const hourlyData = Array.from({length: 24}, () => Math.floor(Math.random() * 80) + 10);
            charts.hourly.data.datasets[0].data = hourlyData;
            charts.hourly.update();
            
            // 更新路段对比图
            const sectionLabels = Object.keys(streams).map(getStreamName);
            const sectionData = Object.keys(streams).map(() => Math.floor(Math.random() * 80) + 10);
            
            charts.section.data.labels = sectionLabels;
            charts.section.data.datasets[0].data = sectionData;
            charts.section.update();
            
            // 更新预测图
            const predictionLabels = [];
            const currentData = [];
            const predictedData = [];
            
            for (let i = 0; i < 12; i++) {
                const time = new Date(now.getTime() + i * 300000); // 每5分钟
                predictionLabels.push(time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }));
                
                if (i < 6) {
                    currentData.push(Math.floor(Math.random() * 60) + 20);
                    predictedData.push(null);
                } else {
                    currentData.push(null);
                    predictedData.push(Math.floor(Math.random() * 80) + 10);
                }
            }
            
            charts.prediction.data.labels = predictionLabels;
            charts.prediction.data.datasets[0].data = currentData;
            charts.prediction.data.datasets[1].data = predictedData;
            charts.prediction.update();
        }
        
        // 添加预警
        function addAlert(title, message, level) {
            const alert = {
                id: Date.now(),
                title: title,
                message: message,
                level: level,
                timestamp: new Date()
            };
            
            alerts.unshift(alert);
            
            // 限制预警数量
            if (alerts.length > 20) {
                alerts = alerts.slice(0, 20);
            }
            
            updateAlertList();
        }
        
        // 更新预警列表
        function updateAlertList() {
            if (alerts.length === 0) {
                alertList.innerHTML = `
                    <div style="text-align: center; color: #86909c; padding: 40px;">
                        暂无拥堵预警
                    </div>
                `;
                return;
            }
            
            alertList.innerHTML = '';
            
            alerts.forEach(alert => {
                const item = document.createElement('div');
                item.className = `alert-item alert-${alert.level}`;
                
                const icon = alert.level === 'high' ? '🚨' : 
                           alert.level === 'medium' ? '⚠️' : 'ℹ️';
                
                item.innerHTML = `
                    <div class="alert-icon">${icon}</div>
                    <div class="alert-content">
                        <div class="alert-title">${alert.title}: ${alert.message}</div>
                        <div class="alert-time">${alert.timestamp.toLocaleString()}</div>
                    </div>
                `;
                
                alertList.appendChild(item);
            });
        }
        
        // 获取流名称
        function getStreamName(streamId) {
            const names = {
                'hangqian_highway_01': 'K10+500 杭州方向',
                'hangqian_highway_02': 'K15+200 千岛湖方向',
                'hangqian_highway_03': 'K20+800 服务区入口',
                'hangqian_highway_04': 'K68+300 昌化镇段',
                'hangqian_highway_05': 'K89+600 汾口收费站',
                'hangqian_highway_06': 'K105+900 千岛湖镇'
            };
            return names[streamId] || streamId;
        }
        
        // 开始实时更新
        function startRealTimeUpdate() {
            updateInterval = setInterval(() => {
                if (analysisActive) {
                    updateAnalysisResults();
                }
            }, 5000); // 每5秒更新一次
        }
        
        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            if (updateInterval) {
                clearInterval(updateInterval);
            }
        });
        
        // 初始化按钮状态
        updateButtonStates();
    </script>
</body>
</html>
