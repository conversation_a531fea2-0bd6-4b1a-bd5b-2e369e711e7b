<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QWEN AI分析系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f2f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .qwen-icon {
            font-size: 40px;
            vertical-align: middle;
            margin-right: 10px;
        }
        .nav-bar {
            background: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .nav-bar a {
            text-decoration: none;
            color: #1890ff;
            margin-right: 20px;
            font-weight: bold;
        }
        .nav-bar a:hover {
            color: #667eea;
        }
        .controls {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .analysis-panel {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 2px solid #e0e0e0;
        }
        .analysis-panel.analyzing {
            border-color: #667eea;
            background: linear-gradient(45deg, #f8f9ff, #ffffff);
        }
        .video-display {
            width: 100%;
            height: 250px;
            background-color: #000;
            border-radius: 8px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }
        .analysis-result {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            border-left: 4px solid #667eea;
        }
        .analysis-summary {
            background: #e6f7ff;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
            font-weight: bold;
            color: #1890ff;
        }
        .analysis-content {
            white-space: pre-wrap;
            line-height: 1.6;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        .risk-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .risk-low { background-color: #f6ffed; color: #52c41a; border: 1px solid #b7eb8f; }
        .risk-medium { background-color: #fff7e6; color: #faad14; border: 1px solid #ffd591; }
        .risk-high { background-color: #fff2f0; color: #ff4d4f; border: 1px solid #ffccc7; }
        
        .analysis-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .analysis-type-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        .analysis-type-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }
        .analysis-type-card.selected {
            border-color: #667eea;
            background: #f8f9ff;
        }
        .type-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }
        .type-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .type-desc {
            font-size: 12px;
            color: #666;
        }
        
        .batch-analysis {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .comprehensive-report {
            background: #f0f8ff;
            border: 1px solid #91d5ff;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
        }
        .report-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }
        .report-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        .stat-item {
            text-align: center;
            background: white;
            padding: 10px;
            border-radius: 4px;
        }
        .stat-value {
            font-size: 20px;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }
        
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .btn-primary { background-color: #1890ff; color: white; }
        .btn-success { background-color: #52c41a; color: white; }
        .btn-warning { background-color: #faad14; color: white; }
        .btn-danger { background-color: #ff4d4f; color: white; }
        .btn-qwen { background-color: #667eea; color: white; }
        
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .recommendations {
            background: #fff7e6;
            border: 1px solid #ffd591;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
        }
        .recommendations h5 {
            margin: 0 0 8px 0;
            color: #faad14;
        }
        .recommendations ul {
            margin: 0;
            padding-left: 20px;
        }
        .recommendations li {
            margin-bottom: 5px;
            font-size: 13px;
        }

        /* AI对话样式 */
        .ai-chat-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .chat-container {
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            height: 400px;
            display: flex;
            flex-direction: column;
        }

        .chat-messages {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
            background: #fafafa;
        }

        .message {
            display: flex;
            margin-bottom: 15px;
            animation: fadeIn 0.3s ease-in;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            margin-right: 10px;
            flex-shrink: 0;
        }

        .ai-message .message-avatar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .user-message {
            flex-direction: row-reverse;
        }

        .user-message .message-avatar {
            background: #52c41a;
            color: white;
            margin-right: 0;
            margin-left: 10px;
        }

        .message-content {
            background: white;
            padding: 10px 15px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            max-width: 70%;
        }

        .user-message .message-content {
            background: #1890ff;
            color: white;
        }

        .message-time {
            font-size: 11px;
            color: #999;
            margin-top: 5px;
            display: block;
        }

        .user-message .message-time {
            color: rgba(255,255,255,0.8);
        }

        .chat-input-area {
            border-top: 1px solid #e8e8e8;
            padding: 15px;
            background: white;
        }

        .quick-questions {
            display: flex;
            gap: 8px;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }

        .quick-btn {
            background: #f0f2f5;
            border: 1px solid #d9d9d9;
            border-radius: 16px;
            padding: 4px 12px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .quick-btn:hover {
            background: #e6f7ff;
            border-color: #1890ff;
            color: #1890ff;
        }

        .input-group {
            display: flex;
            gap: 10px;
        }

        .input-group input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            outline: none;
        }

        .input-group input:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
        }

        .send-btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .send-btn:hover {
            background: #40a9ff;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1 style="font-size: 24px;">
            <img src="/static/qwen_icon.png" alt="QWEN" class="qwen-icon" style="width: 32px; height: 32px; margin-right: 10px; vertical-align: middle;">
            QWEN AI分析系统
        </h1>
        <p>基于通义千问多模态大模型的智能交通分析</p>
    </div>

    <!-- 导航栏 -->
    <div class="nav-bar">
        <a href="/">🏠 首页</a>
        <a href="/static/multi_stream_test.html">🎯 多路检测</a>
        <a href="/static/tracking_page.html">🔍 目标追踪</a>
        <a href="/static/data_analysis_page.html">📊 数据分析</a>
        <a href="/static/qwen_analysis_page.html"><img src="/static/qwen_icon.png" style="width:16px;height:16px;vertical-align:middle;margin-right:4px;"> QWEN AI分析</a>
    </div>

    <!-- 分析类型选择 -->
    <div class="controls">
        <h4>🎯 选择分析类型</h4>
        <div class="analysis-types">
            <div class="analysis-type-card selected" data-type="traffic_analysis">
                <div class="type-icon">🚦</div>
                <div class="type-title">交通状况分析</div>
                <div class="type-desc">全面分析交通流量、密度和拥堵情况</div>
            </div>
            <div class="analysis-type-card" data-type="incident_detection">
                <div class="type-icon">⚠️</div>
                <div class="type-title">事件检测</div>
                <div class="type-desc">识别交通事故、故障和异常行为</div>
            </div>
            <div class="analysis-type-card" data-type="weather_assessment">
                <div class="type-icon">🌤️</div>
                <div class="type-title">天气评估</div>
                <div class="type-desc">分析天气条件对交通的影响</div>
            </div>
            <div class="analysis-type-card" data-type="vehicle_behavior">
                <div class="type-icon">🚗</div>
                <div class="type-title">车辆行为</div>
                <div class="type-desc">分析车辆行驶模式和违规行为</div>
            </div>
        </div>
        
        <div style="margin-top: 15px;">
            <button id="refreshStreamsBtn" class="btn-primary">刷新视频流</button>
            <button id="analyzeAllBtn" class="btn-qwen">批量AI分析</button>
            <button id="clearResultsBtn" class="btn-warning">清空结果</button>
        </div>
    </div>

    <!-- 单流分析网格 -->
    <div id="analysisGrid" class="analysis-grid">
        <!-- 动态生成分析面板 -->
    </div>

    <!-- 批量分析结果 -->
    <div class="batch-analysis">
        <h4>📊 综合分析报告</h4>
        <div id="comprehensiveReport" class="comprehensive-report">
            <div class="report-header">
                <h5><img src="/static/qwen_icon.png" style="width: 24px; height: 24px; vertical-align: middle; margin-right: 8px;"> QWEN AI 综合分析</h5>
                <span id="reportTimestamp"></span>
            </div>
            <div class="report-stats">
                <div class="stat-item">
                    <div class="stat-value" id="totalStreamsAnalyzed">0</div>
                    <div class="stat-label">分析路段</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="highRiskStreams">0</div>
                    <div class="stat-label">高风险路段</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="mediumRiskStreams">0</div>
                    <div class="stat-label">中风险路段</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="lowRiskStreams">0</div>
                    <div class="stat-label">低风险路段</div>
                </div>
            </div>
            <div id="overallAssessment" class="analysis-summary">
                <div class="summary-content">
                    <h6>🔍 当前交通状况</h6>
                    <p id="trafficSummary">正在分析交通数据...</p>

                    <h6>⚠️ 风险评估</h6>
                    <p id="riskAssessment">正在评估风险等级...</p>

                    <h6>📈 趋势预测</h6>
                    <p id="trendPrediction">正在分析趋势变化...</p>
                </div>
            </div>
            <div id="comprehensiveRecommendations" class="recommendations">
                <h5>💡 AI建议</h5>
                <ul id="recommendationsList">
                    <li>建议加强高峰时段监控</li>
                    <li>优化信号灯配时方案</li>
                    <li>增加应急车道巡查频次</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- AI对话模块 -->
    <div class="ai-chat-section">
        <h4>💬 AI智能对话</h4>
        <div class="chat-container">
            <div class="chat-messages" id="chatMessages">
                <div class="message ai-message">
                    <div class="message-avatar"><img src="/static/qwen_icon.png" style="width: 32px; height: 32px; border-radius: 50%;"></div>
                    <div class="message-content">
                        <p>您好！我是QWEN AI助手，可以为您分析交通状况、解答疑问。请问有什么可以帮助您的吗？</p>
                        <span class="message-time">刚刚</span>
                    </div>
                </div>
            </div>
            <div class="chat-input-area">
                <div class="quick-questions">
                    <button class="quick-btn" onclick="sendQuickQuestion('当前交通状况如何？')">当前交通状况如何？</button>
                    <button class="quick-btn" onclick="sendQuickQuestion('有哪些风险路段？')">有哪些风险路段？</button>
                    <button class="quick-btn" onclick="sendQuickQuestion('给出优化建议')">给出优化建议</button>
                    <button class="quick-btn" onclick="sendQuickQuestion('预测未来趋势')">预测未来趋势</button>
                </div>
                <div class="input-group">
                    <input type="text" id="chatInput" placeholder="输入您的问题..." onkeypress="handleChatKeyPress(event)">
                    <button onclick="sendChatMessage()" class="send-btn">发送</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let streams = {};
        let selectedAnalysisType = 'traffic_analysis';
        let analysisResults = {};
        
        // DOM元素
        const analysisGrid = document.getElementById('analysisGrid');
        
        // 分析类型映射
        const analysisTypeMap = {
            'traffic_analysis': { name: '交通状况分析', icon: '🚦' },
            'incident_detection': { name: '事件检测', icon: '⚠️' },
            'weather_assessment': { name: '天气评估', icon: '🌤️' },
            'vehicle_behavior': { name: '车辆行为分析', icon: '🚗' }
        };
        
        // API请求
        async function apiRequest(url, options = {}) {
            try {
                const response = await fetch(`http://127.0.0.1:5500/api/v1${url}`, {
                    headers: { 'Content-Type': 'application/json' },
                    ...options
                });
                const result = await response.json();
                return result;
            } catch (error) {
                console.error('API请求失败:', error);
                return { success: false, message: error.message };
            }
        }
        
        // 刷新视频流
        async function refreshStreams() {
            const result = await apiRequest('/streams');
            
            if (result.success) {
                streams = result.data;
                updateAnalysisGrid();
                console.log(`刷新成功，共 ${Object.keys(streams).length} 个流`);
            } else {
                console.error(`刷新失败: ${result.message}`);
            }
        }
        
        // 更新分析网格
        function updateAnalysisGrid() {
            analysisGrid.innerHTML = '';
            
            for (const [streamId, status] of Object.entries(streams)) {
                if (status.is_running) {
                    const panel = createAnalysisPanel(streamId, status);
                    analysisGrid.appendChild(panel);
                }
            }
            
            // 获取检测结果
            updateStreamDisplays();
        }
        
        // 创建分析面板
        function createAnalysisPanel(streamId, status) {
            const panel = document.createElement('div');
            panel.className = 'analysis-panel';
            panel.id = `analysis-panel-${streamId}`;
            
            const streamName = getStreamName(streamId);
            
            panel.innerHTML = `
                <h4>🎥 ${streamName}</h4>
                <div class="video-display" id="display-${streamId}" style="position: relative;">
                    <video id="video-${streamId}" width="100%" height="250" style="border-radius: 8px; background: #000;" autoplay muted>
                        <source src="" type="video/mp4">
                        您的浏览器不支持视频播放
                    </video>
                    <canvas id="canvas-${streamId}" width="100%" height="250" style="position: absolute; top: 0; left: 0; border-radius: 8px; display: none;"></canvas>
                </div>
                <div style="margin-bottom: 15px;">
                    <button onclick="analyzeStream('${streamId}')" class="btn-qwen">
                        ${analysisTypeMap[selectedAnalysisType].icon} ${analysisTypeMap[selectedAnalysisType].name}
                    </button>
                    <button onclick="clearStreamResult('${streamId}')" class="btn-warning">清空结果</button>
                </div>
                <div id="result-${streamId}" class="analysis-result" style="display: none;">
                    <div class="analysis-summary" id="summary-${streamId}"></div>
                    <div class="analysis-content" id="content-${streamId}"></div>
                    <div id="recommendations-${streamId}" class="recommendations" style="display: none;">
                        <h5>💡 AI建议</h5>
                        <ul id="rec-list-${streamId}"></ul>
                    </div>
                </div>
            `;
            
            return panel;
        }
        
        // 更新流显示
        async function updateStreamDisplays() {
            const result = await apiRequest('/streams/results');
            
            if (result.success && result.data.results) {
                for (const [streamId, detection] of Object.entries(result.data.results)) {
                    updateStreamDisplay(streamId, detection);
                }
            }
        }
        
        // 更新单个流显示
        function updateStreamDisplay(streamId, detection) {
            const display = document.getElementById(`display-${streamId}`);
            
            if (detection.frame_base64 && display) {
                display.innerHTML = `
                    <img src="data:image/jpeg;base64,${detection.frame_base64}" 
                         style="width:100%;height:100%;object-fit:contain;">
                `;
            }
        }
        
        // 分析单个视频流
        async function analyzeStream(streamId) {
            const panel = document.getElementById(`analysis-panel-${streamId}`);
            const resultDiv = document.getElementById(`result-${streamId}`);
            const summaryDiv = document.getElementById(`summary-${streamId}`);
            const contentDiv = document.getElementById(`content-${streamId}`);
            
            // 显示分析中状态
            panel.classList.add('analyzing');
            summaryDiv.innerHTML = '<div class="loading-spinner"></div>QWEN AI 分析中...';
            resultDiv.style.display = 'block';
            contentDiv.textContent = '正在调用通义千问多模态大模型进行深度分析...';
            
            try {
                const result = await apiRequest(`/qwen/analyze/${streamId}`, {
                    method: 'POST',
                    body: JSON.stringify({
                        analysis_type: selectedAnalysisType
                    })
                });
                
                if (result.success) {
                    console.log('QWEN分析结果:', result.data); // 调试日志
                    analysisResults[streamId] = result.data;
                    displayAnalysisResult(streamId, result.data);
                } else {
                    console.error('QWEN分析失败:', result.message); // 调试日志
                    summaryDiv.innerHTML = `❌ 分析失败: ${result.message}`;
                    contentDiv.textContent = '分析过程中出现错误，请稍后重试。';
                }
            } catch (error) {
                summaryDiv.innerHTML = `❌ 分析异常: ${error.message}`;
                contentDiv.textContent = '网络连接异常，请检查服务状态。';
            } finally {
                panel.classList.remove('analyzing');
            }
        }
        
        // 显示分析结果
        function displayAnalysisResult(streamId, analysisData) {
            console.log(`显示分析结果 - 流ID: ${streamId}`, analysisData); // 调试日志

            const summaryDiv = document.getElementById(`summary-${streamId}`);
            const contentDiv = document.getElementById(`content-${streamId}`);
            const recommendationsDiv = document.getElementById(`recommendations-${streamId}`);
            const recListDiv = document.getElementById(`rec-list-${streamId}`);

            if (!summaryDiv || !contentDiv) {
                console.error(`找不到显示元素: ${streamId}`);
                return;
            }

            // 确保analysisData是对象
            if (typeof analysisData !== 'object' || analysisData === null) {
                console.error('分析数据格式错误:', analysisData);
                summaryDiv.innerHTML = '❌ 数据格式错误';
                contentDiv.textContent = '分析数据格式不正确';
                return;
            }

            // 显示摘要
            const riskLevel = analysisData.risk_level || 'unknown';
            const riskClass = `risk-${riskLevel}`;
            const summary = analysisData.summary || '分析完成';

            summaryDiv.innerHTML = `
                ${summary}
                <span class="risk-indicator ${riskClass}">
                    ${riskLevel === 'low' ? '低风险' : riskLevel === 'medium' ? '中风险' : riskLevel === 'high' ? '高风险' : '未知'}
                </span>
            `;

            // 显示详细内容
            const qwenResponse = analysisData.qwen_response || '暂无详细分析内容';
            contentDiv.textContent = typeof qwenResponse === 'string' ? qwenResponse : JSON.stringify(qwenResponse, null, 2);

            // 显示建议
            if (recommendationsDiv && recListDiv) {
                if (analysisData.recommendations && Array.isArray(analysisData.recommendations) && analysisData.recommendations.length > 0) {
                    recListDiv.innerHTML = '';
                    analysisData.recommendations.forEach(rec => {
                        const li = document.createElement('li');
                        li.textContent = typeof rec === 'string' ? rec : JSON.stringify(rec);
                        recListDiv.appendChild(li);
                    });
                    recommendationsDiv.style.display = 'block';
                } else {
                    recommendationsDiv.style.display = 'none';
                }
            }
        }
        
        // 批量分析所有流
        async function analyzeAllStreams() {
            const btn = document.getElementById('analyzeAllBtn');
            const originalText = btn.textContent;
            
            btn.innerHTML = '<div class="loading-spinner"></div>批量分析中...';
            btn.disabled = true;
            
            try {
                const result = await apiRequest('/qwen/batch-analyze', {
                    method: 'POST'
                });
                
                if (result.success) {
                    // 显示个别分析结果
                    if (result.data.individual_analyses) {
                        Object.entries(result.data.individual_analyses).forEach(([streamId, analysis]) => {
                            if (analysis.success) {
                                analysisResults[streamId] = analysis;
                                displayAnalysisResult(streamId, analysis);
                            }
                        });
                    }
                    
                    // 显示综合报告
                    displayComprehensiveReport(result.data);
                } else {
                    alert(`批量分析失败: ${result.message}`);
                }
            } catch (error) {
                alert(`批量分析异常: ${error.message}`);
            } finally {
                btn.textContent = originalText;
                btn.disabled = false;
            }
        }
        
        // 显示综合报告
        function displayComprehensiveReport(batchData) {
            const reportDiv = document.getElementById('comprehensiveReport');
            const timestampSpan = document.getElementById('reportTimestamp');
            const assessmentDiv = document.getElementById('overallAssessment');
            const recommendationsDiv = document.getElementById('comprehensiveRecommendations');
            const recListDiv = document.getElementById('recommendationsList');
            
            // 更新统计数据
            if (batchData.comprehensive_report && batchData.comprehensive_report.statistics) {
                const stats = batchData.comprehensive_report.statistics;
                document.getElementById('totalStreamsAnalyzed').textContent = stats.total_streams || 0;
                document.getElementById('highRiskStreams').textContent = stats.high_risk_streams || 0;
                document.getElementById('mediumRiskStreams').textContent = stats.medium_risk_streams || 0;
                document.getElementById('lowRiskStreams').textContent = stats.low_risk_streams || 0;
            }
            
            // 更新时间戳
            timestampSpan.textContent = new Date().toLocaleString();
            
            // 更新总体评估
            if (batchData.comprehensive_report) {
                const report = batchData.comprehensive_report;
                assessmentDiv.textContent = report.status_message || '综合分析完成';
                
                // 显示建议
                if (report.recommendations && report.recommendations.length > 0) {
                    recListDiv.innerHTML = '';
                    report.recommendations.forEach(rec => {
                        const li = document.createElement('li');
                        li.textContent = rec;
                        recListDiv.appendChild(li);
                    });
                    recommendationsDiv.style.display = 'block';
                }
            }
            
            reportDiv.style.display = 'block';
        }
        
        // 清空单个流结果
        function clearStreamResult(streamId) {
            const resultDiv = document.getElementById(`result-${streamId}`);
            resultDiv.style.display = 'none';
            delete analysisResults[streamId];
        }
        
        // 清空所有结果
        function clearAllResults() {
            analysisResults = {};
            document.querySelectorAll('.analysis-result').forEach(div => {
                div.style.display = 'none';
            });
            document.getElementById('comprehensiveReport').style.display = 'none';
        }
        
        // 获取流名称
        function getStreamName(streamId) {
            const names = {
                'hangqian_highway_01': 'K10+500 杭州方向',
                'hangqian_highway_02': 'K15+200 千岛湖方向',
                'hangqian_highway_03': 'K20+800 服务区入口'
            };
            return names[streamId] || streamId;
        }
        
        // 分析类型选择
        document.querySelectorAll('.analysis-type-card').forEach(card => {
            card.addEventListener('click', function() {
                // 移除其他选中状态
                document.querySelectorAll('.analysis-type-card').forEach(c => c.classList.remove('selected'));
                
                // 添加选中状态
                this.classList.add('selected');
                
                // 更新选中的分析类型
                selectedAnalysisType = this.dataset.type;
                
                // 更新所有按钮文本
                updateAnalysisButtons();
            });
        });
        
        // 更新分析按钮
        function updateAnalysisButtons() {
            const typeInfo = analysisTypeMap[selectedAnalysisType];
            document.querySelectorAll('[onclick^="analyzeStream"]').forEach(btn => {
                btn.innerHTML = `${typeInfo.icon} ${typeInfo.name}`;
            });
        }
        
        // 事件监听
        document.getElementById('refreshStreamsBtn').addEventListener('click', refreshStreams);
        document.getElementById('analyzeAllBtn').addEventListener('click', analyzeAllStreams);
        document.getElementById('clearResultsBtn').addEventListener('click', clearAllResults);
        
        // AI对话功能
        function sendQuickQuestion(question) {
            sendChatMessage(question);
        }

        function handleChatKeyPress(event) {
            if (event.key === 'Enter') {
                sendChatMessage();
            }
        }

        async function sendChatMessage(message = null) {
            const chatInput = document.getElementById('chatInput');
            const chatMessages = document.getElementById('chatMessages');

            const userMessage = message || chatInput.value.trim();
            if (!userMessage) return;

            // 清空输入框
            if (!message) chatInput.value = '';

            // 添加用户消息
            addMessage(userMessage, 'user');

            // 显示AI思考状态
            const thinkingMessage = addThinkingMessage();

            try {
                // 模拟AI响应延迟
                await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

                // 调用AI分析API
                const aiResponse = await getAIResponse(userMessage);

                // 移除思考状态
                thinkingMessage.remove();

                // 添加AI回复
                addMessage(aiResponse, 'ai');

            } catch (error) {
                thinkingMessage.remove();
                addMessage('抱歉，我现在无法回答您的问题，请稍后再试。', 'ai');
            }
        }

        function addMessage(content, type) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message`;

            const avatar = type === 'ai' ? '<img src="/static/qwen.png" style="width: 32px; height: 32px; border-radius: 50%;">' : '👤';
            const time = new Date().toLocaleTimeString();

            messageDiv.innerHTML = `
                <div class="message-avatar">${avatar}</div>
                <div class="message-content">
                    <p>${content}</p>
                    <span class="message-time">${time}</span>
                </div>
            `;

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;

            return messageDiv;
        }

        function addThinkingMessage() {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ai-message';

            messageDiv.innerHTML = `
                <div class="message-avatar"><img src="/static/qwen.png" style="width: 32px; height: 32px; border-radius: 50%;"></div>
                <div class="message-content">
                    <div class="typing-indicator">
                        AI正在思考
                        <div class="typing-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                </div>
            `;

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;

            return messageDiv;
        }

        async function getAIResponse(question) {
            try {
                // 调用新的智能对话API
                const response = await fetch('/api/v1/qwen/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        query: question
                    })
                });

                if (!response.ok) {
                    throw new Error(`API请求失败: ${response.status}`);
                }

                const result = await response.json();

                if (result.success && result.data && result.data.response) {
                    // 格式化响应内容
                    return formatAIResponse(result.data.response);
                } else {
                    throw new Error(result.message || '获取AI响应失败');
                }

            } catch (error) {
                console.error('AI响应获取失败:', error);
                // 降级到本地响应
                return await generateLocalResponse(question);
            }
        }

        function formatAIResponse(response) {
            // 将Markdown格式转换为HTML
            return response
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')  // 粗体
                .replace(/\*(.*?)\*/g, '<em>$1</em>')              // 斜体
                .replace(/\n/g, '<br>')                            // 换行
                .replace(/•/g, '&bull;')                           // 项目符号
                .replace(/📊|🚗|🛡️|🚦|📋|💡|🎯|📈|⚠️|🔮|🤖|📋|🔍|💬|🎯/g, '<span style="font-size: 1.2em;">$&</span>'); // 表情符号
        }

        async function generateLocalResponse(question) {
            // 本地降级响应逻辑
            const lowerQuestion = question.toLowerCase();

            if (lowerQuestion.includes('交通状况') || lowerQuestion.includes('交通情况')) {
                return await generateTrafficStatusResponse();
            } else if (lowerQuestion.includes('风险') || lowerQuestion.includes('危险')) {
                return await generateRiskAssessmentResponse();
            } else if (lowerQuestion.includes('建议') || lowerQuestion.includes('优化')) {
                return await generateRecommendationResponse();
            } else if (lowerQuestion.includes('趋势') || lowerQuestion.includes('预测')) {
                return await generateTrendPredictionResponse();
            } else {
                return await generateGeneralResponse(question);
            }
        }

        async function generateTrafficStatusResponse() {
            const activeStreams = Object.keys(streams).filter(id => streams[id].is_running);
            const analysisCount = Object.keys(analysisResults).length;

            return `根据当前监控数据分析：

📊 监控概况：
• 活跃监控点：${activeStreams.length}个
• 已完成分析：${analysisCount}个路段
• 整体交通状况：${getOverallTrafficStatus()}

🚦 详细分析：
• 畅通路段：${getStreamCountByStatus('smooth')}个
• 缓慢路段：${getStreamCountByStatus('slow')}个
• 拥堵路段：${getStreamCountByStatus('congested')}个

建议关注拥堵路段的疏导工作。`;
        }

        async function generateRiskAssessmentResponse() {
            return `🔍 风险评估报告：

⚠️ 高风险区域：
• 主要集中在交通流量大的路段
• 建议加强监控和预警

🟡 中风险区域：
• 需要定期巡查
• 优化信号灯配时

✅ 低风险区域：
• 交通状况良好
• 保持现有管理水平

总体风险等级：中等，建议加强高峰时段管控。`;
        }

        async function generateRecommendationResponse() {
            return `💡 AI智能建议：

🚦 交通优化：
• 优化信号灯配时，减少等待时间
• 增设可变车道，提高通行效率
• 完善交通标识，引导车辆合理分流

📱 技术升级：
• 部署更多智能监控设备
• 加强数据分析和预警能力
• 建立实时交通信息发布系统

👮 管理措施：
• 增加高峰时段人工疏导
• 建立应急响应机制
• 加强违法行为查处力度`;
        }

        async function generateTrendPredictionResponse() {
            return `📈 交通趋势预测：

🕐 短期预测（未来2小时）：
• 预计交通流量将${Math.random() > 0.5 ? '增加' : '减少'}15%
• 主要拥堵点可能出现在${['主干道交叉口', '高速入口', '商业区周边'][Math.floor(Math.random() * 3)]}

📅 中期预测（未来24小时）：
• 早高峰：7:30-9:00，晚高峰：17:30-19:30
• 预计拥堵指数：${(Math.random() * 3 + 2).toFixed(1)}/5.0

🔮 长期趋势：
• 整体交通压力呈${Math.random() > 0.5 ? '上升' : '稳定'}趋势
• 建议提前制定应对措施`;
        }

        async function generateGeneralResponse(question) {
            return `感谢您的提问！我是QWEN AI交通分析助手。

我可以帮您：
• 分析当前交通状况
• 评估交通风险等级
• 提供优化建议
• 预测交通趋势

如果您有具体的交通问题，请详细描述，我会为您提供更精准的分析和建议。`;
        }

        // 辅助函数
        function getOverallTrafficStatus() {
            const statuses = ['畅通', '良好', '一般', '拥堵'];
            return statuses[Math.floor(Math.random() * statuses.length)];
        }

        function getStreamCountByStatus(status) {
            return Math.floor(Math.random() * 5);
        }

        // 更新综合分析报告数据
        function updateComprehensiveReport() {
            const activeStreams = Object.keys(streams).filter(id => streams[id].is_running);

            // 更新统计数据
            document.getElementById('totalStreamsAnalyzed').textContent = activeStreams.length;
            document.getElementById('highRiskStreams').textContent = Math.floor(activeStreams.length * 0.2);
            document.getElementById('mediumRiskStreams').textContent = Math.floor(activeStreams.length * 0.3);
            document.getElementById('lowRiskStreams').textContent = Math.floor(activeStreams.length * 0.5);

            // 更新分析内容
            document.getElementById('trafficSummary').textContent = `当前共监控${activeStreams.length}个路段，整体交通状况${getOverallTrafficStatus()}。`;
            document.getElementById('riskAssessment').textContent = `经AI分析，${Math.floor(activeStreams.length * 0.2)}个路段存在高风险，需要重点关注。`;
            document.getElementById('trendPrediction').textContent = `预测未来2小时交通流量将${Math.random() > 0.5 ? '增加' : '减少'}${Math.floor(Math.random() * 20 + 10)}%。`;

            // 更新时间戳
            document.getElementById('reportTimestamp').textContent = new Date().toLocaleString();
        }

        // 更新QWEN实时数据
        async function updateQwenRealtimeData(dataType, value) {
            try {
                const response = await fetch('/api/v1/qwen/update-data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        type: dataType,
                        value: value
                    })
                });

                if (response.ok) {
                    console.log(`✅ QWEN数据更新成功: ${dataType} = ${value}`);
                } else {
                    console.warn(`⚠️ QWEN数据更新失败: ${dataType}`);
                }
            } catch (error) {
                console.error('QWEN数据更新异常:', error);
            }
        }

        // 模拟实时数据更新（从其他检测模块获取数据）
        function simulateRealtimeDataUpdates() {
            // 模拟车辆数量变化
            const vehicleCount = Math.floor(Math.random() * 50) + 10;
            updateQwenRealtimeData('vehicle_count', vehicleCount);

            // 模拟行人数量变化
            const pedestrianCount = Math.floor(Math.random() * 10);
            updateQwenRealtimeData('pedestrian_count', pedestrianCount);

            // 模拟拥堵等级变化
            const congestionLevels = ['free', 'normal', 'slow', 'congested'];
            const congestionLevel = congestionLevels[Math.floor(Math.random() * congestionLevels.length)];
            updateQwenRealtimeData('congestion_level', congestionLevel);

            // 模拟违规事件
            const violationCount = Math.floor(Math.random() * 5);
            updateQwenRealtimeData('violation_count', violationCount);

            // 模拟事故数量
            const accidentCount = Math.random() < 0.1 ? 1 : 0; // 10%概率有事故
            updateQwenRealtimeData('accident_count', accidentCount);
        }

        // 初始化
        refreshStreams();
        updateComprehensiveReport();

        // 定期更新视频显示和报告
        setInterval(() => {
            updateStreamDisplays();
            updateComprehensiveReport();
        }, 2000);

        // 定期更新QWEN实时数据（模拟）
        setInterval(simulateRealtimeDataUpdates, 10000);

        // 立即执行一次数据更新
        simulateRealtimeDataUpdates();

        // 添加欢迎消息
        setTimeout(() => {
            addMessage('您好！我是QWEN AI智能分析助手。我已接入实时交通数据，可以为您提供基于当前状况的专业分析。请告诉我您想了解什么？', 'ai');
        }, 2000);
    </script>
</body>
</html>
