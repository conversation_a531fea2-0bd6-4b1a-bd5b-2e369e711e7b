<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>违规检测系统 - 高速公路YOLO智能监控</title>
    
    <!-- Arco Design CSS -->
    <link rel="stylesheet" href="https://unpkg.com/@arco-design/web-react@2.62.0/dist/css/arco.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f7f8fa;
            color: #1d2129;
        }
        
        /* 顶部导航栏 */
        .top-navbar {
            background: linear-gradient(135deg, #faad14 0%, #fa8c16 100%);
            color: white;
            padding: 0 24px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .navbar-left {
            display: flex;
            align-items: center;
        }
        
        .system-logo {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 20px;
            background-image: url('/static/logo.webp');
            background-size: cover;
            background-position: center;
        }
        
        .system-name {
            font-size: 20px;
            font-weight: 600;
        }
        
        .back-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.3s;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        /* 主内容区 */
        .main-content {
            padding: 24px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        /* 页面标题 */
        .page-header {
            margin-bottom: 24px;
        }
        
        .page-title {
            font-size: 22px;
            font-weight: 600;
            color: #1d2129;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .page-subtitle {
            color: #86909c;
            font-size: 16px;
        }
        
        /* 违规类型选择 */
        .violation-types {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid #e5e6eb;
        }
        
        .types-header {
            font-size: 18px;
            font-weight: 600;
            color: #1d2129;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .types-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }
        
        .violation-type {
            background: #f7f8fa;
            border: 2px solid #e5e6eb;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .violation-type:hover {
            border-color: #faad14;
            background: #fff7e6;
        }
        
        .violation-type.active {
            border-color: #faad14;
            background: #fff7e6;
            box-shadow: 0 2px 8px rgba(250, 173, 20, 0.2);
        }
        
        .type-icon {
            font-size: 32px;
            margin-bottom: 8px;
        }
        
        .type-name {
            font-weight: 600;
            color: #1d2129;
            margin-bottom: 4px;
        }
        
        .type-desc {
            font-size: 12px;
            color: #86909c;
        }
        
        /* 控制面板 */
        .control-panel {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid #e5e6eb;
        }
        
        .control-header {
            font-size: 18px;
            font-weight: 600;
            color: #1d2129;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .control-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .control-label {
            font-size: 14px;
            font-weight: 500;
            color: #4e5969;
        }
        
        .control-input {
            padding: 8px 12px;
            border: 1px solid #e5e6eb;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .control-input:focus {
            outline: none;
            border-color: #faad14;
            box-shadow: 0 0 0 2px rgba(250, 173, 20, 0.1);
        }
        
        .control-buttons {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #faad14, #fa8c16);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
        }
        
        .btn-success {
            background: #00b42a;
            color: white;
        }
        
        .btn-warning {
            background: #faad14;
            color: white;
        }
        
        .btn-danger {
            background: #f53f3f;
            color: white;
        }
        
        /* 检测网格 */
        .detection-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 24px;
            margin-bottom: 24px;
        }
        
        .detection-panel {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid #e5e6eb;
            position: relative;
        }
        
        .detection-panel.violation {
            border-color: #faad14;
            box-shadow: 0 4px 16px rgba(250, 173, 20, 0.2);
            animation: violationBlink 2s infinite;
        }
        
        @keyframes violationBlink {
            0%, 100% { box-shadow: 0 4px 16px rgba(250, 173, 20, 0.2); }
            50% { box-shadow: 0 4px 16px rgba(250, 173, 20, 0.5); }
        }
        
        .panel-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e5e6eb;
        }
        
        .panel-title {
            font-size: 16px;
            font-weight: 600;
            color: #1d2129;
        }
        
        .panel-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-normal {
            background: #e8f5e8;
            color: #00b42a;
        }
        
        .status-monitoring {
            background: #e6f7ff;
            color: #1890ff;
        }
        
        .status-violation {
            background: #fff7e6;
            color: #faad14;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .video-display {
            width: 100%;
            height: 280px;
            background: #000;
            border-radius: 8px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            position: relative;
            overflow: hidden;
        }
        
        .violation-overlay {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(250, 173, 20, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            display: none;
        }
        
        .detection-info {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .info-item {
            background: #f7f8fa;
            padding: 12px;
            border-radius: 6px;
            text-align: center;
        }
        
        .info-value {
            font-size: 18px;
            font-weight: 600;
            color: #1d2129;
            margin-bottom: 4px;
        }
        
        .info-label {
            font-size: 12px;
            color: #86909c;
        }
        
        .panel-controls {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }
        
        /* 违规记录 */
        .violation-records {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid #e5e6eb;
        }
        
        .records-header {
            font-size: 18px;
            font-weight: 600;
            color: #1d2129;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .records-filter {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        
        .filter-select {
            padding: 6px 12px;
            border: 1px solid #e5e6eb;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .records-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .record-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 12px;
            border-left: 4px solid #faad14;
            background: #fff7e6;
            transition: all 0.3s;
        }
        
        .record-item:hover {
            background: #fff2d3;
            transform: translateX(4px);
        }
        
        .record-icon {
            margin-right: 16px;
            font-size: 24px;
        }
        
        .record-content {
            flex: 1;
        }
        
        .record-title {
            font-weight: 600;
            color: #1d2129;
            margin-bottom: 4px;
        }
        
        .record-details {
            font-size: 14px;
            color: #86909c;
            margin-bottom: 8px;
        }
        
        .record-time {
            font-size: 12px;
            color: #faad14;
            font-weight: 500;
        }
        
        .record-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .action-btn.process {
            background: #00b42a;
            color: white;
        }
        
        .action-btn.details {
            background: #1890ff;
            color: white;
        }
        
        .action-btn.export {
            background: #faad14;
            color: white;
        }
        
        /* 统计面板 */
        .stats-panel {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid #e5e6eb;
            margin-bottom: 24px;
        }
        
        .stats-header {
            font-size: 18px;
            font-weight: 600;
            color: #1d2129;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #f7f8fa, #ffffff);
            border: 1px solid #e5e6eb;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        
        .stat-icon {
            font-size: 32px;
            margin-bottom: 12px;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #1d2129;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 14px;
            color: #86909c;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .detection-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .control-grid {
                grid-template-columns: 1fr;
            }
            
            .types-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="top-navbar">
        <div class="navbar-left">
            <div class="system-logo"></div>
            <div class="system-name">违规检测系统</div>
        </div>
        <a href="/static/dashboard.html" class="back-btn">
            <span>←</span>
            <span>返回控制台</span>
        </a>
    </nav>
    
    <!-- 主内容区 -->
    <main class="main-content">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">
                <span>🚫</span>
                <span>违规检测系统</span>
            </h1>
            <p class="page-subtitle">高速公路交通违规行为智能检测与处罚系统</p>
        </div>
        
        <!-- 违规类型选择 -->
        <div class="violation-types">
            <div class="types-header">
                <span>🎯</span>
                <span>违规类型选择</span>
            </div>
            
            <div class="types-grid">
                <div class="violation-type active" data-type="speeding">
                    <div class="type-icon">🏃</div>
                    <div class="type-name">超速行驶</div>
                    <div class="type-desc">检测车辆超速违规</div>
                </div>
                
                <div class="violation-type" data-type="lane_change">
                    <div class="type-icon">↔️</div>
                    <div class="type-name">违规变道</div>
                    <div class="type-desc">检测违规变道行为</div>
                </div>
                
                <div class="violation-type" data-type="emergency_lane">
                    <div class="type-icon">🚨</div>
                    <div class="type-name">占用应急车道</div>
                    <div class="type-desc">检测应急车道占用</div>
                </div>
                
                <div class="violation-type" data-type="following">
                    <div class="type-icon">🚗</div>
                    <div class="type-name">跟车过近</div>
                    <div class="type-desc">检测跟车距离违规</div>
                </div>
                
                <div class="violation-type" data-type="reverse">
                    <div class="type-icon">↩️</div>
                    <div class="type-name">逆向行驶</div>
                    <div class="type-desc">检测逆向行驶违规</div>
                </div>
                
                <div class="violation-type" data-type="parking">
                    <div class="type-icon">🅿️</div>
                    <div class="type-name">违规停车</div>
                    <div class="type-desc">检测违规停车行为</div>
                </div>
            </div>
        </div>
        
        <!-- 控制面板 -->
        <div class="control-panel">
            <div class="control-header">
                <span>🎛️</span>
                <span>检测控制</span>
            </div>
            
            <div class="control-grid">
                <div class="control-group">
                    <label class="control-label">检测精度</label>
                    <select class="control-input" id="precision">
                        <option value="standard">标准精度</option>
                        <option value="high" selected>高精度</option>
                        <option value="ultra">超高精度</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label class="control-label">速度阈值(km/h)</label>
                    <input type="number" class="control-input" id="speedLimit" value="120" min="60" max="200">
                </div>
                
                <div class="control-group">
                    <label class="control-label">置信度阈值</label>
                    <input type="number" class="control-input" id="confidence" value="0.8" min="0.1" max="1.0" step="0.1">
                </div>
                
                <div class="control-group">
                    <label class="control-label">检测间隔(秒)</label>
                    <input type="number" class="control-input" id="interval" value="2" min="1" max="10">
                </div>
            </div>
            
            <div class="control-buttons">
                <button class="btn btn-primary" id="startDetectionBtn">
                    <span>▶️</span>
                    <span>启动检测</span>
                </button>
                <button class="btn btn-warning" id="pauseDetectionBtn">
                    <span>⏸️</span>
                    <span>暂停检测</span>
                </button>
                <button class="btn btn-danger" id="stopDetectionBtn">
                    <span>⏹️</span>
                    <span>停止检测</span>
                </button>
                <button class="btn btn-success" id="refreshStreamsBtn">
                    <span>🔄</span>
                    <span>刷新视频流</span>
                </button>
            </div>
        </div>
        
        <!-- 统计面板 -->
        <div class="stats-panel">
            <div class="stats-header">
                <span>📊</span>
                <span>违规统计</span>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">🚫</div>
                    <div class="stat-value" id="totalViolations">0</div>
                    <div class="stat-label">总违规数</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">🏃</div>
                    <div class="stat-value" id="speedingCount">0</div>
                    <div class="stat-label">超速违规</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">📹</div>
                    <div class="stat-value" id="monitoringStreams">0</div>
                    <div class="stat-label">监控路段</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">💰</div>
                    <div class="stat-value" id="totalFines">¥0</div>
                    <div class="stat-label">罚款金额</div>
                </div>
            </div>
        </div>
        
        <!-- 检测网格 -->
        <div id="detectionGrid" class="detection-grid">
            <!-- 动态生成检测面板 -->
        </div>
        
        <!-- 违规记录 -->
        <div class="violation-records">
            <div class="records-header">
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span>📋</span>
                    <span>违规记录</span>
                </div>
                <div class="records-filter">
                    <select class="filter-select" id="violationFilter">
                        <option value="">所有违规</option>
                        <option value="speeding">超速行驶</option>
                        <option value="lane_change">违规变道</option>
                        <option value="emergency_lane">占用应急车道</option>
                        <option value="following">跟车过近</option>
                        <option value="reverse">逆向行驶</option>
                        <option value="parking">违规停车</option>
                    </select>
                    <button class="btn btn-primary btn-small" onclick="exportViolations()">
                        导出记录
                    </button>
                </div>
            </div>
            
            <div id="recordsList" class="records-list">
                <div style="text-align: center; color: #86909c; padding: 40px;">
                    暂无违规记录
                </div>
            </div>
        </div>
    </main>

    <script>
        // 全局变量
        let streams = {};
        let detectionActive = false;
        let updateInterval = null;
        let violationCount = 0;
        let speedingCount = 0;
        let totalFines = 0;
        let violations = [];
        let selectedViolationTypes = ['speeding'];
        
        // DOM元素
        const detectionGrid = document.getElementById('detectionGrid');
        const recordsList = document.getElementById('recordsList');
        
        // 违规类型配置
        const violationConfig = {
            speeding: { name: '超速行驶', icon: '🏃', fine: 200 },
            lane_change: { name: '违规变道', icon: '↔️', fine: 150 },
            emergency_lane: { name: '占用应急车道', icon: '🚨', fine: 200 },
            following: { name: '跟车过近', icon: '🚗', fine: 100 },
            reverse: { name: '逆向行驶', icon: '↩️', fine: 500 },
            parking: { name: '违规停车', icon: '🅿️', fine: 200 }
        };
        
        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            refreshStreams();
            setupEventListeners();
            startRealTimeUpdate();
        });
        
        // 设置事件监听器
        function setupEventListeners() {
            document.getElementById('startDetectionBtn').addEventListener('click', startDetection);
            document.getElementById('pauseDetectionBtn').addEventListener('click', pauseDetection);
            document.getElementById('stopDetectionBtn').addEventListener('click', stopDetection);
            document.getElementById('refreshStreamsBtn').addEventListener('click', refreshStreams);
            
            // 违规类型选择
            document.querySelectorAll('.violation-type').forEach(type => {
                type.addEventListener('click', function() {
                    this.classList.toggle('active');
                    updateSelectedViolationTypes();
                });
            });
            
            // 违规筛选
            document.getElementById('violationFilter').addEventListener('change', filterViolations);
        }
        
        // 更新选中的违规类型
        function updateSelectedViolationTypes() {
            selectedViolationTypes = [];
            document.querySelectorAll('.violation-type.active').forEach(type => {
                selectedViolationTypes.push(type.dataset.type);
            });
        }
        
        // API请求
        async function apiRequest(url, options = {}) {
            try {
                const response = await fetch(`http://127.0.0.1:5500/api/v1${url}`, {
                    headers: { 'Content-Type': 'application/json' },
                    ...options
                });
                const result = await response.json();
                return result;
            } catch (error) {
                console.error('API请求失败:', error);
                return { success: false, message: error.message };
            }
        }
        
        // 刷新视频流
        async function refreshStreams() {
            const result = await apiRequest('/streams');
            
            if (result.success) {
                streams = result.data;
                updateDetectionGrid();
                updateStats();
                console.log(`刷新成功，共 ${Object.keys(streams).length} 个流`);
            } else {
                console.error(`刷新失败: ${result.message}`);
            }
        }
        
        // 更新检测网格
        function updateDetectionGrid() {
            detectionGrid.innerHTML = '';
            
            for (const [streamId, status] of Object.entries(streams)) {
                if (status.is_running) {
                    const panel = createDetectionPanel(streamId, status);
                    detectionGrid.appendChild(panel);
                }
            }
            
            // 获取检测结果
            updateDetectionResults();
        }
        
        // 创建检测面板
        function createDetectionPanel(streamId, status) {
            const panel = document.createElement('div');
            panel.className = 'detection-panel';
            panel.id = `detection-panel-${streamId}`;
            
            const streamName = getStreamName(streamId);
            
            panel.innerHTML = `
                <div class="panel-header">
                    <div class="panel-title">${streamName}</div>
                    <div class="panel-status status-normal" id="status-${streamId}">正常</div>
                </div>
                
                <div class="video-display" id="display-${streamId}" style="position: relative;">
                    <video id="video-${streamId}" width="100%" height="280" style="border-radius: 8px; background: #000;" autoplay muted>
                        <source src="" type="video/mp4">
                        您的浏览器不支持视频播放
                    </video>
                    <canvas id="canvas-${streamId}" width="100%" height="280" style="position: absolute; top: 0; left: 0; border-radius: 8px; display: none;"></canvas>
                    <div class="violation-overlay" id="overlay-${streamId}" style="position: absolute; top: 10px; left: 10px; background: rgba(255,0,0,0.8); color: white; padding: 5px 10px; border-radius: 4px; display: none;">🚫 检测到违规</div>
                </div>
                
                <div class="detection-info">
                    <div class="info-item">
                        <div class="info-value" id="vehicles-${streamId}">0</div>
                        <div class="info-label">车辆数量</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value" id="speed-${streamId}">0</div>
                        <div class="info-label">平均速度</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value" id="violations-${streamId}">0</div>
                        <div class="info-label">违规次数</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value" id="fines-${streamId}">¥0</div>
                        <div class="info-label">罚款金额</div>
                    </div>
                </div>
                
                <div class="panel-controls">
                    <button class="btn btn-primary btn-small" onclick="enableViolationDetection('${streamId}')">
                        启用检测
                    </button>
                    <button class="btn btn-warning btn-small" onclick="disableViolationDetection('${streamId}')">
                        禁用检测
                    </button>
                    <button class="btn btn-danger btn-small" onclick="simulateViolation('${streamId}')">
                        模拟违规
                    </button>
                </div>
            `;
            
            return panel;
        }
        
        // 启动检测
        async function startDetection() {
            detectionActive = true;
            updateButtonStates();
            
            // 为所有流启用违规检测
            for (const streamId of Object.keys(streams)) {
                await enableViolationDetection(streamId);
            }
            
            addViolationRecord('系统启动', '违规检测系统已启动', 'system', 'system');
        }
        
        // 暂停检测
        function pauseDetection() {
            detectionActive = false;
            updateButtonStates();
            addViolationRecord('系统暂停', '违规检测系统已暂停', 'system', 'system');
        }
        
        // 停止检测
        async function stopDetection() {
            detectionActive = false;
            updateButtonStates();

            // 停止语音报警
            await stopVoiceAlert();

            // 为所有流禁用违规检测
            for (const streamId of Object.keys(streams)) {
                await disableViolationDetection(streamId);
            }

            addViolationRecord('系统停止', '违规检测系统已停止', 'system', 'system');
        }

        // 语音播报函数（使用简单的Web Speech API）
        function speakAlert(message) {
            try {
                if ('speechSynthesis' in window) {
                    const utterance = new SpeechSynthesisUtterance(message);
                    utterance.lang = 'zh-CN';
                    utterance.rate = 1.2;
                    utterance.pitch = 1.1;
                    utterance.volume = 0.8;

                    // 选择中文语音
                    const voices = speechSynthesis.getVoices();
                    const chineseVoice = voices.find(voice => voice.lang.includes('zh'));
                    if (chineseVoice) {
                        utterance.voice = chineseVoice;
                    }

                    speechSynthesis.speak(utterance);
                    console.log('🔊 语音播报:', message);
                }
            } catch (error) {
                console.log('语音播报失败:', error);
            }
        }

        // 停止语音报警
        async function stopVoiceAlert() {
            try {
                // 停止前端语音播放
                stopFrontendVoice();
                console.log('✅ 违规检测语音报警已停止');
            } catch (error) {
                console.error('❌ 停止违规检测语音报警异常:', error);
            }
        }

        // 停止前端语音播放
        function stopFrontendVoice() {
            try {
                // 停止Web Speech API
                if (window.speechSynthesis) {
                    window.speechSynthesis.cancel();
                    console.log('✅ 已停止Web Speech API语音');
                }

                // 停止所有audio元素
                const audioElements = document.querySelectorAll('audio');
                audioElements.forEach(audio => {
                    audio.pause();
                    audio.currentTime = 0;
                });

                if (audioElements.length > 0) {
                    console.log(`✅ 已停止 ${audioElements.length} 个音频元素`);
                }

            } catch (error) {
                console.error('❌ 停止前端语音失败:', error);
            }
        }
        
        // 启用违规检测
        async function enableViolationDetection(streamId) {
            const result = await apiRequest(`/violation/enable/${streamId}`, {
                method: 'POST',
                body: JSON.stringify({
                    types: selectedViolationTypes,
                    precision: document.getElementById('precision').value,
                    speed_limit: parseInt(document.getElementById('speedLimit').value),
                    confidence_threshold: parseFloat(document.getElementById('confidence').value),
                    detection_interval: parseInt(document.getElementById('interval').value)
                })
            });
            
            if (result.success) {
                updatePanelStatus(streamId, '监控中', 'monitoring');
                console.log(`违规检测启用成功: ${streamId}`);
            } else {
                console.error(`违规检测启用失败: ${result.message}`);
                addViolationRecord('启用失败', `${getStreamName(streamId)} 违规检测启用失败`, streamId, 'system');
            }
        }
        
        // 禁用违规检测
        async function disableViolationDetection(streamId) {
            const result = await apiRequest(`/violation/disable/${streamId}`, {
                method: 'POST'
            });
            
            if (result.success) {
                updatePanelStatus(streamId, '正常', 'normal');
                console.log(`违规检测禁用成功: ${streamId}`);
            } else {
                console.error(`违规检测禁用失败: ${result.message}`);
            }
        }
        
        // 模拟违规
        function simulateViolation(streamId) {
            const violationTypes = Object.keys(violationConfig);
            const randomType = violationTypes[Math.floor(Math.random() * violationTypes.length)];
            const config = violationConfig[randomType];
            
            const streamName = getStreamName(streamId);
            updatePanelStatus(streamId, '违规', 'violation');
            
            // 显示违规覆盖层
            const overlay = document.getElementById(`overlay-${streamId}`);
            if (overlay) {
                overlay.textContent = `🚫 ${config.name}`;
                overlay.style.display = 'block';
            }
            
            // 添加违规记录
            addViolationRecord(config.name, `${streamName} 检测到${config.name}`, streamId, randomType);
            
            // 更新面板统计
            updatePanelStats(streamId, randomType, config.fine);
            
            // 3秒后恢复
            setTimeout(() => {
                updatePanelStatus(streamId, '监控中', 'monitoring');
                if (overlay) {
                    overlay.style.display = 'none';
                }
            }, 3000);
        }
        
        // 更新面板状态
        function updatePanelStatus(streamId, statusText, statusClass) {
            const statusEl = document.getElementById(`status-${streamId}`);
            const panel = document.getElementById(`detection-panel-${streamId}`);
            
            if (statusEl) {
                statusEl.textContent = statusText;
                statusEl.className = `panel-status status-${statusClass}`;
            }
            
            if (panel) {
                panel.className = `detection-panel ${statusClass === 'violation' ? 'violation' : ''}`;
            }
        }
        
        // 更新面板统计
        function updatePanelStats(streamId, violationType, fine) {
            const violationsEl = document.getElementById(`violations-${streamId}`);
            const finesEl = document.getElementById(`fines-${streamId}`);
            
            if (violationsEl) {
                const current = parseInt(violationsEl.textContent) || 0;
                violationsEl.textContent = current + 1;
            }
            
            if (finesEl) {
                const current = parseInt(finesEl.textContent.replace('¥', '')) || 0;
                finesEl.textContent = `¥${current + fine}`;
            }
            
            // 更新全局统计
            violationCount++;
            if (violationType === 'speeding') {
                speedingCount++;
            }
            totalFines += fine;
            updateStats();
        }
        
        // 更新按钮状态
        function updateButtonStates() {
            const startBtn = document.getElementById('startDetectionBtn');
            const pauseBtn = document.getElementById('pauseDetectionBtn');
            const stopBtn = document.getElementById('stopDetectionBtn');
            
            if (detectionActive) {
                startBtn.disabled = true;
                pauseBtn.disabled = false;
                stopBtn.disabled = false;
            } else {
                startBtn.disabled = false;
                pauseBtn.disabled = true;
                stopBtn.disabled = true;
            }
        }
        
        // 更新检测结果
        async function updateDetectionResults() {
            const result = await apiRequest('/streams/results');

            if (result.success && result.data.results) {
                for (const [streamId, detection] of Object.entries(result.data.results)) {
                    updateStreamDisplay(streamId, detection);

                    // 模拟违规检测结果
                    if (detectionActive) {
                        simulateViolationDetection(streamId, detection);
                    }
                }
            }
        }
        
        // 更新流显示
        function updateStreamDisplay(streamId, detection) {
            const display = document.getElementById(`display-${streamId}`);
            
            if (detection.frame_base64 && display) {
                display.innerHTML = `
                    <div class="violation-overlay" id="overlay-${streamId}" style="display: none;">🚫 检测到违规</div>
                    <img src="data:image/jpeg;base64,${detection.frame_base64}" 
                         style="width:100%;height:100%;object-fit:contain;">
                `;
            }
            
            // 更新检测信息
            updateDetectionInfo(streamId, detection);
        }
        
        // 更新检测信息
        function updateDetectionInfo(streamId, detection) {
            const vehiclesEl = document.getElementById(`vehicles-${streamId}`);
            const speedEl = document.getElementById(`speed-${streamId}`);
            
            if (vehiclesEl) vehiclesEl.textContent = detection.total_detections || 0;
            if (speedEl) speedEl.textContent = Math.floor(Math.random() * 40) + 80 + ' km/h';
        }
        
        // 模拟违规检测
        function simulateViolationDetection(streamId, detection) {
            // 随机生成违规检测结果
            if (Math.random() < 0.05) { // 5% 概率检测到违规
                const violationTypes = selectedViolationTypes.length > 0 ? selectedViolationTypes : ['speeding'];
                const randomType = violationTypes[Math.floor(Math.random() * violationTypes.length)];
                const config = violationConfig[randomType];
                
                const streamName = getStreamName(streamId);
                updatePanelStatus(streamId, '违规', 'violation');
                
                // 显示违规覆盖层
                const overlay = document.getElementById(`overlay-${streamId}`);
                if (overlay) {
                    overlay.textContent = `🚫 ${config.name}`;
                    overlay.style.display = 'block';
                }
                
                addViolationRecord(config.name, `${streamName} 检测到${config.name}`, streamId, randomType);
                updatePanelStats(streamId, randomType, config.fine);
                
                // 3秒后恢复
                setTimeout(() => {
                    updatePanelStatus(streamId, '监控中', 'monitoring');
                    if (overlay) {
                        overlay.style.display = 'none';
                    }
                }, 3000);
            }
        }
        
        // 添加违规记录
        function addViolationRecord(title, description, streamId, violationType) {
            const violation = {
                id: Date.now(),
                title: title,
                description: description,
                streamId: streamId,
                type: violationType,
                timestamp: new Date(),
                status: 'pending',
                fine: violationConfig[violationType]?.fine || 0
            };
            
            violations.unshift(violation);
            
            // 限制违规记录数量
            if (violations.length > 100) {
                violations = violations.slice(0, 100);
            }
            
            updateViolationList();

            // 触发语音播报（仅对违规行为，不包括系统消息）
            if (violationType && violationType !== 'system') {
                const config = violationConfig[violationType] || { name: title };
                speakAlert(`检测到${config.name}：${description}`);
            }
        }
        
        // 更新违规列表
        function updateViolationList() {
            const filteredViolations = filterViolationsByType();
            
            if (filteredViolations.length === 0) {
                recordsList.innerHTML = `
                    <div style="text-align: center; color: #86909c; padding: 40px;">
                        暂无违规记录
                    </div>
                `;
                return;
            }
            
            recordsList.innerHTML = '';
            
            filteredViolations.forEach(violation => {
                const item = document.createElement('div');
                item.className = 'record-item';
                
                const config = violationConfig[violation.type] || { icon: '🚫', name: violation.title };
                
                item.innerHTML = `
                    <div class="record-icon">${config.icon}</div>
                    <div class="record-content">
                        <div class="record-title">${violation.title}</div>
                        <div class="record-details">${violation.description} - 罚款: ¥${violation.fine}</div>
                        <div class="record-time">${violation.timestamp.toLocaleString()}</div>
                    </div>
                    <div class="record-actions">
                        <button class="action-btn process" onclick="processViolation(${violation.id})">
                            处理
                        </button>
                        <button class="action-btn details" onclick="viewViolationDetails(${violation.id})">
                            详情
                        </button>
                        <button class="action-btn export" onclick="exportSingleViolation(${violation.id})">
                            导出
                        </button>
                    </div>
                `;
                
                recordsList.appendChild(item);
            });
        }
        
        // 筛选违规记录
        function filterViolationsByType() {
            const filterType = document.getElementById('violationFilter').value;
            if (!filterType) return violations;
            return violations.filter(v => v.type === filterType);
        }
        
        // 筛选违规
        function filterViolations() {
            updateViolationList();
        }
        
        // 处理违规
        function processViolation(violationId) {
            const violation = violations.find(v => v.id === violationId);
            if (violation) {
                violation.status = 'processed';
                updateViolationList();
                alert(`违规记录已处理: ${violation.title}`);
            }
        }
        
        // 查看违规详情
        function viewViolationDetails(violationId) {
            const violation = violations.find(v => v.id === violationId);
            if (violation) {
                alert(`违规详情:\n\n类型: ${violation.title}\n描述: ${violation.description}\n时间: ${violation.timestamp.toLocaleString()}\n罚款: ¥${violation.fine}\n状态: ${violation.status}`);
            }
        }
        
        // 导出单个违规记录
        function exportSingleViolation(violationId) {
            const violation = violations.find(v => v.id === violationId);
            if (violation) {
                const data = JSON.stringify(violation, null, 2);
                downloadFile(`violation_${violationId}.json`, data);
            }
        }
        
        // 导出所有违规记录
        function exportViolations() {
            const data = JSON.stringify(violations, null, 2);
            downloadFile(`violations_${new Date().toISOString().split('T')[0]}.json`, data);
        }
        
        // 下载文件
        function downloadFile(filename, content) {
            const blob = new Blob([content], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.click();
            URL.revokeObjectURL(url);
        }
        
        // 更新统计信息
        function updateStats() {
            document.getElementById('totalViolations').textContent = violationCount;
            document.getElementById('speedingCount').textContent = speedingCount;
            document.getElementById('monitoringStreams').textContent = Object.keys(streams).length;
            document.getElementById('totalFines').textContent = `¥${totalFines}`;
        }
        
        // 获取流名称
        function getStreamName(streamId) {
            const names = {
                'hangqian_highway_01': 'K10+500 杭州方向',
                'hangqian_highway_02': 'K15+200 千岛湖方向',
                'hangqian_highway_03': 'K20+800 服务区入口',
                'hangqian_highway_04': 'K68+300 昌化镇段',
                'hangqian_highway_05': 'K89+600 汾口收费站',
                'hangqian_highway_06': 'K105+900 千岛湖镇'
            };
            return names[streamId] || streamId;
        }
        
        // 开始实时更新
        function startRealTimeUpdate() {
            updateInterval = setInterval(() => {
                if (detectionActive) {
                    updateDetectionResults();
                }
            }, 2000); // 每2秒更新一次
        }
        
        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            if (updateInterval) {
                clearInterval(updateInterval);
            }
        });
        
        // 初始化按钮状态
        updateButtonStates();
    </script>
</body>
</html>
